import {
  animate,
  keyframes,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { Novel } from '@schemas/Novel';
import { EventService } from '@services/event.service';
@Component({
  selector: '[app-novel-info-popup]',
  templateUrl: './novel-info.component.html',
  styleUrl: './novel-info.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, NovelCardV2Component],
  animations: [
    trigger('ShowAnimation', [
      transition(':enter', [
        animate(
          500,
          keyframes([
            style({ opacity: 0, offset: 0 }),
            style({ opacity: 0, offset: 0.7, transform: 'scale(0.95)' }),
            style({ opacity: 1, offset: 1, transform: 'scale(1)' }),
          ]),
        ),
      ]),
      transition(':leave', [
        animate(
          200,
          keyframes([
            style({ opacity: 1, offset: 0 }),
            style({ opacity: 0, offset: 1 }),
          ]),
        ),
      ]),
    ]),
  ],
  standalone: true,
})
export class NovelDetailPopupComponent implements AfterViewChecked {
  novel?: Novel;
  constructor(
    private eventService: EventService,
    private cd: ChangeDetectorRef,
  ) {}
  lastPos = { x: 0, y: 0 };
  @ViewChild('novelInfo') element!: ElementRef<HTMLElement>;
  // ngOnInit() { }
  @HostListener('window:mousemove', ['$event'])
  onmousemove(event: MouseEvent) {
    if (!this.element || !this.novel) return;
    this.lastPos.x = event.clientX;
    this.lastPos.y = event.clientY;
    this.goToPostion({ x: event.clientX, y: event.clientY });
  }

  @HostListener('window:scroll', ['$event'])
  onscroll(event: MouseEvent) {
    if (!this.element || !this.novel) return;
    this.novel = undefined;
  }

  goToPostion(position: { x: number; y: number }) {
    if (
      position.y + this.element.nativeElement.offsetHeight >
      window.innerHeight - 100
    ) {
      this.element.nativeElement.style.top =
        position.y - this.element.nativeElement.offsetHeight - 20 + 'px';
    } else {
      this.element.nativeElement.style.top = position.y + 20 + 'px';
    }
    let pos = Math.min(
      Math.max(position.x - 0.5 * this.element.nativeElement.offsetWidth, 0),
      window.innerWidth - this.element.nativeElement.offsetWidth,
    );
    this.element.nativeElement.style.left = pos + 'px';
  }

  reComputePosition() {}

  ngOnInit(): void {
    this.eventService.OnShowComicDetail.subscribe((novel) => {
      if (this.novel != novel) {
        this.novel = novel;

        this.cd.detectChanges();
      }
    });
  }

  ngAfterViewChecked() {
    if (!this.element || !this.novel) return;
    this.goToPostion(this.lastPos);
  }
}
