.faq-section {
  @apply w-full py-8;
}

.faq-container {
  @apply mx-auto px-4;
}

.faq-title {
  @apply text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center;
}

.faq-list {
  @apply space-y-4;
}

.faq-item {
  @apply bg-white dark:bg-dark-700 rounded-lg shadow-sm border border-gray-200 dark:border-dark-600 overflow-hidden transition-all duration-300;

  &.expanded {
    @apply shadow-md;
  }
}

.faq-question {
  @apply w-full px-6 py-4 text-left flex items-center justify-between bg-transparent border-none cursor-pointer transition-colors duration-200;
  @apply hover:bg-gray-50 dark:hover:bg-dark-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset;

  .question-text {
    @apply text-lg font-semibold text-gray-900 dark:text-white pr-4;
  }
}

.faq-icon {
  @apply w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-300 flex-shrink-0;

  &.rotated {
    @apply transform rotate-180;
  }
}

.faq-answer {
  @apply overflow-hidden transition-all duration-300;
  max-height: 0;

  .faq-item.expanded & {
    max-height: 500px;
  }
}

.answer-content {
  @apply px-6 pb-4 text-gray-700 dark:text-gray-300;

  p {
    @apply leading-relaxed;

    strong {
      @apply font-semibold text-gray-900 dark:text-white;
    }

    a {
      @apply text-blue-600 dark:text-blue-400 hover:underline;
    }
  }
}

// Responsive design
@media (max-width: 640px) {
  .faq-question {
    @apply px-4 py-3;

    .question-text {
      @apply text-base;
    }
  }

  .answer-content {
    @apply px-4;
  }
}
