import { CommonModule } from '@angular/common';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { NgModule } from '@angular/core';
import { DateAgoPipe } from './date-ago.pine';
import { NovelDescriptionPipe } from './description.pipe';
import { GenrePipe } from './genre.pine';
import { NumeralPipe } from './numeral.pipe';
import { SafeHtmlPipe } from './safeHTML.pipe';

@NgModule({
  declarations: [GenrePipe, NovelDescriptionPipe, SafeHtmlPipe],
  exports: [GenrePipe, NovelDescriptionPipe, SafeHtmlPipe],
  imports: [CommonModule],
  providers: [provideHttpClient(withInterceptorsFromDi())],
})
export class PinesModule {}
