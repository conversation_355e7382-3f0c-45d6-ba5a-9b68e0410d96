import { NgModule } from '@angular/core';
import { UserRoutingModule } from './user.routing';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserPageComponent } from './page/account.component';
import { UpdateInfoFormComponent } from './update-info-form/update-info-form.component';
import { DialogComponent } from '@components/dialog/dialog.component';
import { ProfileInfoComponent } from './components/profile-info/profile-info.component';
import { BookcaseComponent } from './components/bookcase/bookcase.component';
import { UserSettingsComponent } from './components/user-settings/user-settings.component';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';

@NgModule({
  declarations: [
    UserPageComponent,
    UpdateInfoFormComponent,
    ProfileInfoComponent,
    BookcaseComponent,
    UserSettingsComponent,
  ],
  imports: [
    UserRoutingModule,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    DialogComponent,
    ImgLayoutComponent
  ],
})
export class UserModule {}
