import { CommonModule, isPlatformServer } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  Inject,
  PLATFORM_ID,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Novel } from '@schemas/Novel';
import { DynamicLoadingService } from '@services/dynamic.loading.service'; // Adjust the path as needed

import { EmptyComponent } from '@components/empty/empty.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { AccountService } from '@services/account.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import globalConfig from 'globalConfig';
@Component({
  selector: '[app-follow]',
  standalone: true,
  templateUrl: './follow.component.html',
  styleUrl: './follow.component.scss',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    NovelCardV1Component,
    PaginationComponent,
    SpinnerComponent,
    EmptyComponent,
  ],
})
export class FollowComponent {
  novels: Novel[] = [];
  totalpage!: number;
  currentPage = 1;
  isLoading: boolean = true;
  isLogin: boolean = false;
  selectedNovel: Novel | undefined = {} as Novel;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private dynamicService: DynamicLoadingService,
    private toastService: ToastService,
    private accountService: AccountService,
    private seoService: SeoService,
    private cdr: ChangeDetectorRef,

    @Inject(PLATFORM_ID) private platformId: Object,
  ) {
    seoService.setTitle(`Truyện đang theo dõi`);
  }
  ngOnInit() {
    if (this.isServer()) return;
    this.isLoading = true;

    this.route.queryParams.subscribe((params) => {
      this.isLogin = this.accountService.isAuthenticated();
      this.isLoading = true;
      const page = Number(params['page']) || 1;
      if (this.isLogin) {
        this.reqFollowNovels(page);
      } else {
        this.novels = [];
        this.isLoading = false;
      }
    });
  }
  reqFollowNovels(page = 1) {
    this.accountService.GetFollowedNovels(page).subscribe(
      (res: any) => {
        if (res.status == 404) {
          this.isLoading = false;
        }
        this.novels = res.data.novels;
        this.isLoading = false;
        this.totalpage = res.data.totalpage;
        this.currentPage = page;
        this.cdr.markForCheck();
      },
      (err: any) => {
        console.log(err);
        this.isLoading = false;
        this.cdr.markForCheck();
      },
    );
  }
  isServer() {
    return isPlatformServer(this.platformId);
  }
  OnChangePage(page: number) {
    this.router.navigate(['/tu-sach/theo-doi'], {
      queryParams: { page: page },
      fragment: 'novels',
    });
  }
  trackByFn(index: number) {
    return index;
  }
  onUnfollowNovel(novel: Novel) {
    this.accountService.Follow(novel.id, false).subscribe((res: any) => {
      if (res.status === 1) {
        this.reqFollowNovels();
        this.toastService.show(
          ToastType.Success,
          `Đã bỏ theo dõi ${novel?.title}`,
        );
        this.cdr.markForCheck();
      } else {
        this.toastService.show(ToastType.Error, res.message);
      }
    });
  }

  onConfirm() {
    if (!this.selectedNovel || !this.selectedNovel.id) {
      return;
    }
    this.accountService
      .Follow(this.selectedNovel.id, false)
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.reqFollowNovels();
          this.toastService.show(
            ToastType.Success,
            `Đã bỏ theo dõi ${this.selectedNovel?.title}`,
          );
        } else {
          this.toastService.show(ToastType.Error, res.message);
        }
      });
  }

  get message(): string {
    if (!this.selectedNovel) return '';
    return `Bạn có chắc chắn muốn hủy theo dõi <b>${this.selectedNovel?.title}</b> ?`;
  }
}
