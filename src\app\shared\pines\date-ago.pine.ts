import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateAgo',
  standalone: true,
})
export class DateAgoPipe implements PipeTransform {
  getDateTime(): string {
    return new Date().toLocaleString();
  }
  formatDateTo(dateString: string, format: string): string {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  transform(date?: string, FORMAT_DATE: string = 'DD/MM/YYYY'): string {
    if (!date) {
      return '';
    }
    let dateTime = new Date(date);
    let now = new Date();
    let seconds = (now.getTime() - dateTime.getTime()) / 1000;
    let isOverMonth = seconds >= 3600 * 24 * 30;
    if (isOverMonth) {
      return this.formatDateTo(date!, FORMAT_DATE);
    }
    let days = Math.floor(seconds / 3600 / 24);
    let hours = Math.floor(seconds / 3600) % 24;
    let minutes = Math.floor(seconds / 60) % 60;
    if (days > 0) {
      return days + ' ngày ';
    }
    if (hours > 0) {
      return hours + ' giờ ';
    }
    if (minutes > 0) {
      return minutes + ' phút ';
    }
    return seconds + ' giây ';
  }
}
