<div *ngIf="isOpen" class="confirm-popup-overlay">
  <div class="confirm-popup-content">
    <h2 class="confirm-popup-title">
      <span class="confirm-popup-title-icon">
        <svg
          class="confirm-popup-icon"
          fill="currentColor"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        {{ title }}
      </span>

      <!-- Close Button -->
      <button class="confirm-popup-close-button" (click)="handleCancel()">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="confirm-popup-close-icon"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </h2>

    <p class="confirm-popup-message" [innerHTML]="message"></p>

    <div class="confirm-popup-actions">
      <button class="confirm-popup-cancel-button" (click)="handleCancel()">
        {{ cancelButtonText }}
      </button>
      <button class="confirm-popup-confirm-button" (click)="handleConfirm()">
        {{ confirmButtonText }}
      </button>
    </div>
  </div>
</div>
