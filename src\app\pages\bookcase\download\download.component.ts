import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { EmptyComponent } from '@components/empty/empty.component';
import { ModalComponent } from '@components/modals/modal/modal.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { Novel } from '@schemas/Novel';
import { AccountService } from '@services/account.service';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { NovelService } from '@services/novel.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import { UrlService } from '@services/url.service';
import globalConfig from 'globalConfig';

@Component({
  selector: '[app-download]',
  imports: [
    CommonModule,
    NovelCardV1Component,
    // PaginationComponent,
    SpinnerComponent,
    EmptyComponent,
  ],
  templateUrl: './download.component.html',
  styleUrl: './download.component.scss',
})
export class DownloadComponent {
  novels: Novel[] = [];
  totalpage!: number;
  currentPage = 1;
  isLoading: boolean = true;
  isLogin: boolean = false;
  selectedNovel: Novel | undefined = {} as Novel;
  constructor(
    private novelService: NovelService,
    private accountService: AccountService,
    private dynamicService: DynamicLoadingService,
    private toastService: ToastService,
    private seoService: SeoService,
    private cdr: ChangeDetectorRef,
  ) {
    seoService.setTitle(`Truyện đã tải`);
  }
  ngOnInit() {
    this.isLoading = true;
    this.reqDownloadNovels();
  }
  reqDownloadNovels(page = 1) {
    this.accountService.GetDownloadedNovels(page).subscribe(
      (res: any) => {
        if (res.status == 404) {
          this.isLoading = false;
        }
        this.novels = res.data.novels;
        this.isLoading = false;
        this.totalpage = res.data.totalpage;
        this.currentPage = page;
        this.cdr.markForCheck();
      },
      (err: any) => {
        this.isLoading = false;
      },
    );
  }
  onDownload(novel: Novel) {
    let modalDownloadRef =
      this.dynamicService.createDynamicComponent<ModalComponent>(
        ModalComponent,
      );
    if (!(modalDownloadRef && modalDownloadRef.instance)) return;
    modalDownloadRef.instance.content = `<div class="mt-2"> <p class="text-sm">Bạn có muốn tải truyện <span class="text-primary-100">[${novel?.title}]</span>?</p></div>`;
    modalDownloadRef.instance.title = `Tải truyện`;
    modalDownloadRef.instance.confirmText = `Tải`;
    modalDownloadRef.instance.cancelText = `Hủy`;
    modalDownloadRef?.instance?.openModal();
    modalDownloadRef?.instance?.confirm?.subscribe(() => {
      this.confirmDownload(novel);
    });
  }

  confirmDownload(novel: Novel) {
    if (this.accountService.user!.coin < 10) {
      this.toastService.show(
        ToastType.Error,
        `Xu không đủ, vui lòng nạp thêm xu để tải truyện`,
        5000,
      );
      return;
    }
    let toastid = this.toastService.show(
      ToastType.Success,
      `Đang tải <strong>${novel?.title}</strong>. Vui lòng chờ trong giây lát!`,
      10000,
    );

    this.accountService.downloadNovel(novel?.id!).subscribe({
      next: (data: Blob) => {
        this.toastService.stop(toastid);
        const blob = new Blob([data], { type: data.type });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${novel?.url}.pdf`;
        document.body.appendChild(link); // Append to body to make it work in Firefox
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (err) => {
        this.toastService.clear();
        this.toastService.show(
          ToastType.Error,
          `Đã xảy ra lỗi, vui lòng thử lại`,
          5000,
        );
      },
    });
  }
}
