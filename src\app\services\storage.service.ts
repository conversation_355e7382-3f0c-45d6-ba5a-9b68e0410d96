// src/app/toast.service.ts
import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { SettingOption } from '@schemas/SettingOption';
import { SettingType } from '@schemas/SettingType.enum';
import { User } from '@schemas/User';
import { SsrCookieService } from 'ngx-cookie-service-ssr';

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private ssrCookieService: SsrCookieService,
  ) {}

  SaveSetting(setting: Map<SettingType, SettingOption>) {
    if (isPlatformBrowser(this.platformId)) {
      const jsonObject: Record<string, SettingOption> = {};
      setting.forEach((value, key) => {
        jsonObject[key] = value.value;
      });
      localStorage.setItem('setting', JSON.stringify(jsonObject));
    }
  }

  LoadSetting(setting: Map<SettingType, SettingOption>) {
    if (isPlatformBrowser(this.platformId)) {
      let settingObject = JSON.parse(localStorage.getItem('setting') || '{}');

      setting.forEach((value, key) => {
        value.value = settingObject[key] ?? value.value;
      });
    }
  }

  GetSetting() {
    if (isPlatformBrowser(this.platformId)) {
      let setting = localStorage.getItem('setting');
      if (setting) {
        return JSON.parse(setting);
      }
    }
    return null;
  }

  GetGridType() {
    let type = Number(this.ssrCookieService.get('gridType') || 0);
    return type;
  }
  SetGridType(type: number) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('gridType', type.toString(), 365, '/');
    }
  }

  GetUserData() {
    let user = this.ssrCookieService.get('auth');
    if (user && user != '') {
      return JSON.parse(user);
    }
    return null;
  }

  SetUserData(user: User) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('auth', JSON.stringify(user), 365, '/');
    }
  }

  RemoveUserData() {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('auth', '', 365, '/');
    }
  }

  GetUserDeconfirm() {
    if (isPlatformBrowser(this.platformId)) {
      let user = localStorage.getItem('confrimauth');
      if (user) {
        return JSON.parse(user);
      }
    }
    return null;
  }

  SetUserDeconfirm(user: User) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('confrimauth', JSON.stringify(user));
    }
  }
  GetRememberMeData() {
    if (isPlatformBrowser(this.platformId)) {
      let rememberMeData = localStorage.getItem('rememberMeData');
      if (rememberMeData) {
        return JSON.parse(rememberMeData);
      }
    }
    return null;
  }

  SetRememberMeData(rememberMeData: any) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('rememberMeData', JSON.stringify(rememberMeData));
    }
  }
  GetHistory() {
    if (isPlatformBrowser(this.platformId)) {
      let history = localStorage.getItem('history');
      if (history) {
        return JSON.parse(history as string);
      }
    }
    return [];
  }

  SetHistory(history: any) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('history', JSON.stringify(history));
    }
  }

  IsDarkTheme() {
    return this.ssrCookieService.get('isDarkTheme') === 'true';
  }

  SetDarkTheme(isDarkTheme: boolean) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set(
        'isDarkTheme',
        isDarkTheme.toString(),
        365,
        '/',
      );
    }
  }
}
