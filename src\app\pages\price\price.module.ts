import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TabsComponent } from '@components/tabs/tabs.component';
import { PriceComponent } from './price.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';

@NgModule({
  declarations: [PriceComponent],
  imports: [
    CommonModule,
    TabsComponent,
    BreadcrumbComponent,
    ImgLayoutComponent,
    RouterModule.forChild([
      {
        path: '',
        component: PriceComponent,
        children: [
          {
            path: '',
            redirectTo: 'vip',
            pathMatch: 'full',
          },
          {
            path: 'vip',
            loadComponent: () =>
              import('./vip/vip.component').then((m) => m.VIPComponent),
          },
          {
            path: 'coin',
            loadComponent: () =>
              import('./coin/coin.component').then((m) => m.CoinComponent),
          },
        ],
      },
    ]),
  ],
})
export class PriceModule {}
