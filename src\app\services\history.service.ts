import { Injectable } from '@angular/core';
import { StorageService } from './storage.service';
import { Chapter } from '@schemas/Chapter';
import { Novel } from '@schemas/Novel';
@Injectable({
  providedIn: 'root',
})
export class HistoryService {
  listHistory: Novel[] = [];
  maxHistory = 24;
  constructor(private storageService: StorageService) {
    this.listHistory = this.storageService.GetHistory();
  }

  SaveHistory(comic: Novel, chapter: Chapter) {
    const _comic: any = {
      id: comic.id,
      title: comic.title,
      url: comic.url,
      coverImage: comic.coverImage,
      chapters: [],
    };

    const _chapter: any = {
      id: chapter.id,
      title: chapter.title,
      slug: chapter.slug,
    };

    const oldComic = this.listHistory.find((c) => c.id == comic.id);
    if (oldComic) {
      oldComic.coverImage = comic.coverImage;
      if (!oldComic.chapters) oldComic.chapters = [];
      if (oldComic.chapters.some((c) => c.id == _chapter.id)) return;
      oldComic.chapters.push(_chapter);
    } else {
      this.listHistory.unshift(_comic);
      this.listHistory = this.listHistory.slice(0, this.maxHistory);
      _comic.chapters = [_chapter];
    }
    this.storageService.SetHistory(this.listHistory);
  }

  GetHistorys(): Novel[] {
    return this.listHistory;
  }
  GetHistory(id: number): Novel | undefined {
    return this.listHistory.find((c) => c.id === id);
  }
  GetHistorySize(): number {
    return this.listHistory.length;
  }

  RemoveHistory(id: number) {
    this.listHistory = this.listHistory.filter((c) => c.id != id);
    this.storageService.SetHistory(this.listHistory);
  }
}
