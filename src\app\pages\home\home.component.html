<div
  app-swipper-fade
  #carousel1
  [items]="isServer ? (carouselNovels | slice: 0 : 1) : carouselNovels"
  [autoPlay]="true"
  [delay]="5000"
  class="swiper-container"
>
  <ng-template #maincontent let-item>
    <div class="swiper-main-content">
      <div class="flex flex-col h-full justify-between">
        <span class="flex h-full flex-col">
          <a
            [routerLink]="['/truyen', item.url + '-' + item.id]"
            class="swiper-novel-link"
            [title]="'Đọc truyện ' + item.title + ' online'"
            [attr.aria-label]="'Xem chi tiết truyện ' + item.title"
          >
            <h3 class="swiper-novel-title">
              {{ item.title }}
            </h3>
          </a>

          <a
            [routerLink]="['/tac-gia', item.author]"
            class="text-white flex mb-1 pl-32 sm:pl-[12.5rem] md:pl-[14rem]"
            [title]="'Tác giả: ' + item.author"
            [attr.aria-label]="'Xem thông tin tác giả ' + item.author"
          >
            <div app-icon-author class="size-5"></div>
            <span class="text-sm sm:text-lg line-clamp-1 hover:underline">
              {{ item.author }}
            </span>
          </a>
        </span>
        <div class="flex-start h-full">
          <div
            class="h-44 sm:h-48 rounded-lg flex-start relative w-full xl:w-5/6"
          >
            <div class="absolute z-30 left-2 md:left-5 bottom-8 md:bottom-10">
              <a
                class="flex w-full h-full z-40"
                [routerLink]="['/truyen', item.url + '-' + item.id]"
              >
                <div
                  class="w-28 h-[10.5rem] sm:w-44 sm:h-64 relative overflow-hidden shadow-book"
                >
                  <img
                    [src]="item.coverImage"
                    (error)="
                      item.coverImage =
                        'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'
                    "
                    [alt]="'Bìa truyện ' + item.title + ' - ' + item.author"
                    [title]="item.title"
                    class="w-full h-full object-cover absolute -z-10 bottom-[2px]"
                    loading="lazy"
                    width="176"
                    height="256"
                  />
                  <!-- <div class="absolute top-0 left-0 w-full h-full"></div> -->
                </div>
              </a>
            </div>

            <a
              class="go-to-novel"
              [routerLink]="['/truyen', item.url + '-' + item.id]"
              [title]="'Đọc ngay truyện ' + item.title"
              [attr.aria-label]="'Đọc ngay truyện ' + item.title"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 48 48"
                class="size-7 text-white"
                fill="currentColor"
              >
                <title>Xem</title>
                <g id="Layer_2" data-name="Layer 2">
                  <g id="invisible_box" data-name="invisible box">
                    <rect width="48" height="48" fill="none" />
                  </g>
                  <g id="icons_Q2" data-name="icons Q2">
                    <path
                      d="M44.9,23.2l-38-18L6,5A2,2,0,0,0,4,7L9.3,23H24a2.1,2.1,0,0,1,2,2,2,2,0,0,1-2,2H9.3L4,43a2,2,0,0,0,2,2l.9-.2,38-18A2,2,0,0,0,44.9,23.2Z"
                    />
                  </g>
                </g>
              </svg>
            </a>
            <div class="swiper-mark-bg"></div>
            <div
              class="ml-2.5 pl-[7.5rem] sm:pl-48 md:pl-52 pr-16 py-2 md:py-4 h-full w-full z-30"
            >
              <div
                class="flex gap-2 my-2 w-full flex-wrap max-h-6 overflow-hidden"
              >
                <a
                  *ngFor="let tag of item.genres | slice: 0 : 3"
                  class="genre-tag !bg-neutral-100/80 !border-none dark:text-primary-300 cursor-pointer"
                  [routerLink]="['/the-loai', tag | genre: 'slug']"
                  [title]="'Xem truyện thể loại ' + (tag | genre: 'title')"
                  [attr.aria-label]="
                    'Tìm kiếm truyện thể loại ' + (tag | genre: 'title')
                  "
                >
                  {{ tag | genre: "title" }}
                </a>
              </div>
              <span
                class="swiper-novel-description"
                [innerHTML]="
                  item.description | defaultDescription: 2 : item.title : '3'
                "
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template #bgcontent let-i>
    <div
      class="w-full h-full object-cover bg-cover bg-center bg-no-repeat rounded-lg"
      [ngStyle]="{
        'background-image': 'url(' + carouselNovels[i].coverImage + ')',
      }"
    >
      <!-- <div class="absolute inset-0 z-50"  style="background-image: url('https://www.rophim.me/images/dotted.png')"></div> -->
      <div class="bg-novel-image"></div>
    </div>
  </ng-template>

  <ng-template #navcontent let-index>
    <div
      class="lg:container right-1/2 translate-x-1/2 absolute w-full h-full pointer-events-none z-10"
    >
      <div class="swiper-btn-container">
        <button
          class="swiper-btn"
          (click)="carousel1!.prev()"
          aria-label="Previous"
        >
          <span class="swiper-btn-icon" style="justify-content: center">
            <svg
              class="size-6"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" />
              <polyline points="15 6 9 12 15 18" />
            </svg>
          </span>
        </button>
        <span class="swiper-btn-text"> No. {{ index + 1 }} </span>
        <button
          class="swiper-btn"
          (click)="carousel1!.next()"
          aria-label="Next"
        >
          <span class="swiper-btn-icon" style="justify-content: center"
            ><svg
              class="size-6"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="9 18 15 12 9 6" /></svg
          ></span>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div class="main-container">
  <!-- <div class="flex justify-between"></div> -->
  <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-10 gap-4">
    <div class="grid-cols-1 lg:col-span-3">
      <div app-history-panel class="w-full flex"></div>
    </div>

    <div class="grid-cols-1 md:col-span-2 lg:col-span-7 relative">
      <span class="flex items-center justify-between">
        <span class="flex items-center gap-2">
          <svg
            class="size-6"
            focusable="false"
            viewBox="0 0 24 24"
            fill="currentColor"
            data-testid="StarsIcon"
          >
            <path
              d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm4.24 16L12 15.45 7.77 18l1.12-4.81-3.73-3.23 4.92-.42L12 5l1.92 4.53 4.92.42-3.73 3.23L16.23 18z"
            ></path>
          </svg>
          <h2 class="text-xl font-semibold">TRUYỆN ĐỀ CỬ</h2>
        </span>
        <!-- <button
          class="bg-primary-100 text-white align-middle font-medium text-sm leading-3 rounded size-8"
          (click)="carousel2.next()"
        >
          {{ carousel2.index + 1 }}
        </button> -->
        <div class="btn-conteiner size-8" (click)="carousel2.next()">
          <button class="btn-content" aria-label="Next">
            <svg
              class="size-7"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              stroke-width="2"
              stroke="currentColor"
              fill="none"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path stroke="none" d="M0 0h24v24H0z" />
              <line x1="5" y1="12" x2="19" y2="12" />
              <line x1="13" y1="18" x2="19" y2="12" />
              <line x1="13" y1="6" x2="19" y2="12" />
            </svg>
          </button>
        </div>
      </span>
      <div
        app-carousel
        #carousel2
        [items]="isServer ? [0] : [0, 1, 3]"
        [autoPlay]="false"
        [delay]="10000"
        class="flex relative w-full h-[34rem] md:h-[16.5rem] mt-2"
      >
        <ng-template #maincontent let-item>
          <div
            class="px-1 grid grid-cols-1 h-full md:grid-cols-2 gap-2"
          >
            <ng-container
              *ngFor="
                let novel of recommendNovels | slice: 2 * item : 2 * item + 4;
                index as i
              "
            >
              <div app-card-v2 [novel]="novel" [size]="'cardv2-small'"></div>
            </ng-container>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-1 lg:grid-cols-10 gap-4">
    <div class="h-fit lg:col-span-7">
      <div app-update-panel></div>
    </div>
    <div class="hidden md:block h-fit lg:col-span-3">
      <span class="inline-flex items-center justify-between w-full my-2">
        <span class="inline-flex items-center gap-2">
          <svg
            fill="currentColor"
            class="size-6"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M10 3H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm10 10h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zM17 3c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4zM7 13c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4z"
              ></path>
            </g>
          </svg>

          <h2 class="text-xl font-semibold">THỂ LOẠI</h2>
        </span>

        <a
          routerLink="/the-loai"
          class="text-sm text-primary-100 hover:text-primary-200 font-medium transition-colors"
          title="Xem tất cả thể loại truyện"
        >
          Xem tất cả →
        </a>
      </span>

      <div
        class="shadow-[0px_1px_4px_1px_rgba(0,0,0,0.1)] h-full gap-4 grid grid-cols-3 md:grid-cols-6 lg:grid-cols-2 rounded-2xl p-4 w-full overflow-hidden"
      >
        <a
          [routerLink]="['/the-loai', genre.slug]"
          class="bg-neutral-200/50 dark:bg-dark-600/50 rounded-full flex items-center h-9 hover:bg-neutral-300 dark:hover:bg-dark-700/50 transition-all duration-200 hover:cursor-pointer"
          *ngFor="let genre of listGenres | slice: 0 : 24"
        >
          <!-- <i class="fas fa-dragon mr-2"></i> -->
          <div class="flex-center w-full text-sm">
            <span class="capitalize">{{ genre.title }}</span>
            <!-- <div class="text-gray-400 text-sm">6.1k</div> -->
          </div>
        </a>
      </div>
    </div>
  </div>
  <div
    *ngIf="!isServer && TopNovels"
    class="grid grid-cols-1 lg:grid-cols-3 gap-4"
  >
    <div class="rounded-2xl shadow-common overflow-hidden w-full min-h-[26rem]">
      <div
        app-topday
        [novels]="TopNovels.dailyNovels | slice: 0 : 10"
        class="h-full flex flex-col"
      ></div>
    </div>
    <div class="lg:col-span-2 w-full grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div
        app-top-novel
        class="w-full flex h-full"
        [title]="data.title"
        [novels]="data.novels"
        *ngFor="
          let data of [
            { title: 'TOP TUẦN', novels: TopNovels.weeklyNovels },
            { title: 'TOP THÁNG', novels: TopNovels.monthlyNovels },
          ]
        "
      ></div>
    </div>
  </div>
  <div *ngIf="!isServer" class="flex flex-col">
    <div
      app-grid-card
      [options]="this.options"
      [novels]="completeNovels"
      (selectedValueChange)="onCompleteNovelsChange($event)"
    ></div>
  </div>

  <div class="seo-container flex-flex-col px-4 sm:px-2">
    <h1 class="text-2xl font-semibold mb-4">
      Đọc Truyện Chữ Online Miễn Phí - SayTruyenHot
    </h1>

    <div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">
      <p class="mb-4">
        <strong>SayTruyenHot</strong> là
        <strong>website đọc truyện chữ online miễn phí</strong> với
        <strong>kho truyện đa dạng</strong> chứa nhiều tác phẩm chất lượng cao.
        Chúng tôi mang đến trải nghiệm
        <strong>đọc truyện không quảng cáo</strong>, giao diện đẹp,
        <strong>cập nhật thường xuyên</strong> và hoàn toàn miễn phí.
      </p>

      <h2 class="text-lg font-semibold mt-6 mb-3">
        Tại Sao Chọn SayTruyenHot?
      </h2>
      <ul class="list-disc list-inside mb-4 space-y-1">
        <li>
          <strong>Kho truyện chữ online phong phú</strong> với nhiều đầu truyện
          hay
        </li>
        <li>
          <strong>Truyện full hoàn thành</strong> và cập nhật truyện mới thường
          xuyên
        </li>
        <li>
          <strong>Đọc truyện offline</strong> được với giao diện tối ưu cho mọi
          thiết bị
        </li>
        <li>
          <strong>Website truyện chất lượng</strong> với tính năng theo dõi lịch
          sử đọc
        </li>
        <li>
          <strong>Đọc truyện mượt mà</strong> và hỗ trợ chế độ đọc ban đêm bảo
          vệ mắt
        </li>
        <li>
          <strong>Truyện miễn phí 100%</strong> không cần đăng ký, không giới
          hạn
        </li>
      </ul>

      <h2 class="text-lg font-semibold mt-6 mb-3">
        Thể Loại Truyện Chữ Đa Dạng
      </h2>
      <p class="mb-4">
        Khám phá <strong>nhiều truyện hay</strong> qua đa dạng thể loại:
        <strong>Truyện ngôn tình</strong> lãng mạn,
        <strong>truyện tiên hiệp</strong> hấp dẫn,
        <strong>truyện kiếm hiệp</strong> cổ điển,
        <strong>truyện huyền huyễn</strong> kỳ ảo và
        <strong>truyện đô thị</strong> hiện đại. Mỗi thể loại đều có những
        <strong>truyện chữ chất lượng</strong>, được
        <strong>cập nhật đều đặn</strong>
        để phục vụ nhu cầu giải trí của độc giả.
      </p>

      <h2 class="text-lg font-semibold mt-6 mb-3">
        Đọc Truyện Mọi Lúc Mọi Nơi
      </h2>
      <p class="mb-4">
        <strong>Website đọc truyện online tiện lợi</strong> với khả năng
        <strong>đọc truyện trên điện thoại</strong>, máy tính bảng hay laptop
        đều mượt mà. Hỗ trợ <strong>đọc truyện offline</strong>,
        <strong>giao diện thân thiện</strong> và
        <strong>trải nghiệm thoải mái</strong> giúp bạn thưởng thức những
        <strong>truyện hay</strong> bất cứ lúc nào.
      </p>

      <p class="text-sm text-gray-600 dark:text-gray-400 mt-6">
        Tham gia cộng đồng <strong>đọc truyện chữ online miễn phí</strong> tại
        SayTruyenHot ngay hôm nay để khám phá
        <strong>kho truyện chữ đa dạng</strong> và không bỏ lỡ những tác phẩm
        thú vị!
      </p>
    </div>
  </div>
</div>
