import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Chapter } from '@schemas/Chapter';
import { Genre } from '@schemas/Genre';
import { Novel } from '@schemas/Novel';
import globalConfig from 'globalConfig';

export interface SEOData {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'video';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  keywords?: string;
  category?: string;
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly siteName = globalConfig.APP_NAME;
  private readonly defaultImage = '/logo.png';
  private readonly themeColor = '#ffffff';
  private readonly baseUrl = globalConfig.BASE_URL;

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {}

  /**
   * Clear all dynamic meta tags before setting new ones
   */
  private clearDynamicMetaTags(): void {
    // Remove dynamic meta tags that should be updated per page
    const dynamicTags = [
      'description',
      'author',
      'robots',
      'keywords',
      'category',
      'og:title',
      'og:description',
      'og:type',
      'og:url',
      'og:image',
      'og:site_name',
      'og:locale',
      'twitter:title',
      'twitter:description',
      'twitter:url',
      'twitter:image',
      'twitter:card',
      'article:published_time',
      'article:modified_time',
      'article:section',
      'article:tag',
    ];

    dynamicTags.forEach((tag) => {
      if (
        tag.startsWith('og:') ||
        tag.startsWith('twitter:') ||
        tag.startsWith('article:')
      ) {
        this.meta.removeTag(`property="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });
  }

  /**
   * Generate novel keywords for SEO
   */
  genNovelKeywords(
    novel: Novel,
    chapter?: Chapter,
  ): { title: string; href: string }[] {
    const novelUrl = this.getFullNovelUrl(novel);
    const keywords = [
      { title: `Truyện ${novel.title}`, href: novelUrl },
      { title: `Đọc truyện ${novel.title} online`, href: novelUrl },
      { title: `${novel.title} đầy đủ`, href: novelUrl },
      { title: `${novel.title} miễn phí`, href: novelUrl },
      { title: `Truyện chữ ${novel.title}`, href: novelUrl },
    ];

    if (chapter) {
      const chapterUrl = this.getFullChapterUrl(novel, chapter);
      keywords.push({
        title: `${novel.title} chương ${chapter.slug}`,
        href: chapterUrl,
      });
    }

    return keywords;
  }

  /**
   * Get full novel URL
   */
  getFullNovelUrl(novel: Novel): string {
    return `${this.baseUrl}/truyen/${novel.url}-${novel.id}`;
  }

  /**
   * Get full chapter URL
   */
  getFullChapterUrl(novel: Novel, chapter: Chapter): string {
    return `${this.baseUrl}/${novel.url}/chuong-${chapter.slug}/${chapter.id}`;
  }

  /**
   * Set comprehensive SEO data for a page
   */
  setSEOData(data: SEOData): void {
    // Clear existing dynamic meta tags first
    this.clearDynamicMetaTags();

    // Set title
    const fullTitle = data.title
      ? `${data.title} | ${this.siteName}`
      : this.siteName;
    this.title.setTitle(fullTitle);

    // Get current URL
    const currentUrl = data.url || `${this.baseUrl}${this.router.url}`;

    // Basic meta tags
    this.updateTag({ name: 'description', content: data.description });
    this.updateTag({ name: 'author', content: data.author || this.siteName });

    // Keywords meta tag
    const keywords = data.keywords || this.getDefaultKeywords();
    this.updateTag({ name: 'keywords', content: keywords });

    // Category meta tag
    if (data.category) {
      this.updateTag({ name: 'category', content: data.category });
    }

    // Robots meta
    const robotsContent = this.getRobotsContent(data.noindex, data.nofollow);
    this.updateTag({ name: 'robots', content: robotsContent });

    // Open Graph tags
    this.updateTag({
      property: 'og:title',
      content: data.title || this.siteName,
    });
    this.updateTag({ property: 'og:description', content: data.description });
    this.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.updateTag({ property: 'og:url', content: currentUrl });
    this.updateTag({
      property: 'og:image',
      content: data.image || `${this.baseUrl}${this.defaultImage}`,
    });
    this.updateTag({
      property: 'og:site_name',
      content: data.siteName || this.siteName,
    });
    this.updateTag({ property: 'og:locale', content: data.locale || 'vi_VN' });

    // Article specific tags
    if (data.type === 'article' || data.type === 'book') {
      if (data.publishedTime) {
        this.updateTag({
          property: 'article:published_time',
          content: data.publishedTime,
        });
      }
      if (data.modifiedTime) {
        this.updateTag({
          property: 'article:modified_time',
          content: data.modifiedTime,
        });
      }
      if (data.section) {
        this.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        data.tags.forEach((tag) => {
          this.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    this.updateTag({
      name: 'twitter:card',
      content: data.twitterCard || 'summary_large_image',
    });
    this.updateTag({
      name: 'twitter:title',
      content: data.title || this.siteName,
    });
    this.updateTag({ name: 'twitter:description', content: data.description });
    this.updateTag({
      name: 'twitter:image',
      content: data.image || `${this.baseUrl}${this.defaultImage}`,
    });

    // Canonical URL
    this.updateCanonical(data.canonical || currentUrl);

    // Additional meta tags for better SEO
    this.updateTag({ name: 'theme-color', content: this.themeColor });
    this.updateTag({
      name: 'msapplication-TileColor',
      content: this.themeColor,
    });
  }

  /**
   * Add structured data (JSON-LD)
   */
  addStructuredData(schema: any): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove existing structured data
      this.removeAllStructuredData();

      // Handle both single schema and array of schemas
      const schemas = Array.isArray(schema) ? schema : [schema];

      schemas.forEach((singleSchema, index) => {
        const script = this.document.createElement('script');
        script.type = 'application/ld+json';
        script.text = JSON.stringify(singleSchema, null, 0); // Minified JSON
        script.id = `structured-data-${singleSchema['@type'] === 'Organization' ? 10 + index : index}`;
        script.setAttribute(
          'data-schema-type',
          singleSchema['@type'] || 'Unknown',
        );
        this.document.head.appendChild(script);
      });
    }
  }

  /**
   * Add critical resource preloads for performance
   */
  addCriticalPreloads(
    resources: Array<{
      url: string;
      as: string;
      type?: string;
      crossorigin?: boolean;
    }>,
  ): void {
    if (isPlatformBrowser(this.platformId)) {
      resources.forEach((resource) => {
        const link = this.document.createElement('link');
        link.rel = 'preload';
        link.href = resource.url;
        link.as = resource.as;
        if (resource.type) link.type = resource.type;
        if (resource.crossorigin) link.crossOrigin = 'anonymous';
        link.setAttribute('fetchpriority', 'high');
        this.document.head.appendChild(link);
      });
    }
  }

  /**
   * Add prefetch for next navigation
   */
  addPrefetchLinks(urls: string[]): void {
    if (isPlatformBrowser(this.platformId)) {
      urls.forEach((url) => {
        const link = this.document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        this.document.head.appendChild(link);
      });
    }
  }

  /**
   * Generate FAQ structured data
   */
  generateFAQSchema(faqs: Array<{ question: string; answer: string }>): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map((faq) => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer,
        },
      })),
    };
  }

  /**
   * Remove all existing structured data
   */
  private removeAllStructuredData(): void {
    // Remove all structured data scripts
    const existingScripts = this.document.querySelectorAll(
      'script[type="application/ld+json"]',
    );
    existingScripts.forEach((script) => {
      const type = script.getAttribute('data-schema-type');
      if (type === 'Organization' || type === 'WebSite') return; // Skip organization and website schemas
      if (
        script.id.startsWith('structured-data-') ||
        script.id === 'structured-data'
      ) {
        script.remove();
      }
    });
  }

  /**
   * Set page title
   */
  setTitle(title: string): void {
    const fullTitle = title ? `${title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);
  }

  /**
   * Add meta tags
   */
  addTags(tags: MetaDefinition[]): void {
    this.meta.addTags(tags);
  }

  /**
   * Add single meta tag
   */
  addTag(tag: MetaDefinition): void {
    this.meta.addTag(tag);
  }

  /**
   * Update meta tag
   */
  updateTag(tag: MetaDefinition): void {
    this.meta.updateTag(tag);
  }

  /**
   * Update canonical link
   */
  updateCanonical(url: string): void {
    this.updateLink('canonical', url);
  }

  /**
   * Update link element
   */
  updateLink(rel: string, href: string): void {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(
      `link[rel='${rel}']`,
    );

    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }

    element.setAttribute('rel', rel);
    element.setAttribute('href', href);
  }

  private getDefaultKeywords(): string {
    // Primary high-competition keywords
    const primaryKeywords = [
      'đọc truyện chữ online miễn phí',
      'truyện chữ hay nhất Việt Nam',
      'website đọc truyện số 1',
    ];

    // Long-tail keywords with lower competition
    const longTailKeywords = [
      'truyện chữ full hoàn thành không quảng cáo',
      'đọc truyện ngôn tình ngọt ngào offline',
      'truyện tiên hiệp hay nhất 2025',
      'kho truyện chữ online cập nhật nhanh nhất',
      'đọc truyện miễn phí không cần đăng ký',
    ];

    // Genre-specific keywords
    const genreKeywords = [
      'ngôn tình CEO tổng giá',
      'tiên hiệp tu luyện',
      'kiếm hiệp cổ trang',
      'đô thị dị năng',
      'huyền huyễn xuyên không',
      'trọng sinh báo thù',
      'học đường thanh xuân',
      'cung đấu hoàng gia',
    ];

    // Trending keywords
    const trendingKeywords = [
      'truyện audio có voice',
      'truyện convert chất lượng cao',
      'đọc truyện offline mobile',
      'truyện tranh chữ kết hợp',
    ];

    // Combine all keywords strategically
    return [
      ...primaryKeywords,
      ...longTailKeywords,
      ...genreKeywords,
      ...trendingKeywords,
    ].join(', ');
  }

  addCommonSchema(): void {
    const organizationSchema = this.generateOrganizationSchema();
    const websiteSchema = this.generateWebsiteSchema();
    this.addStructuredData([organizationSchema, websiteSchema]);
  }
  /**
   * Generate genre page structured data
   */
  public generateGenreSchema(genre: Genre, novels: Novel[]): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: `Truyện chữ ${genre.title}`,
      description:
        genre.description || `Kho tàng truyện chữ thể loại ${genre.title}`,
      url: `${this.baseUrl}/the-loai/${genre.slug}`,
      mainEntity: {
        '@type': 'ItemList',
        name: `Danh sách truyện ${genre.title}`,
        numberOfItems: novels.length,
        itemListElement: novels.slice(0, 10).map((novel, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Book',
            name: novel.title,
            url: this.getFullNovelUrl(novel),
            image: novel.coverImage,
            author: novel.author,
            genre: genre.title,
          },
        })),
      },
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Trang chủ',
            item: this.baseUrl,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: `Thể loại ${genre.title}`,
            item: `${this.baseUrl}/the-loai/${genre.slug}`,
          },
        ],
      },
    };
  }

  /**
   * Generate consistent author object for E-A-T signals
   */
  generateAuthorObject(authorName?: string): any {
    if (authorName && authorName.trim()) {
      return {
        '@type': 'Person',
        name: authorName.trim(),
      };
    }
    return undefined;
  }

  /**
   * Get robots content
   */
  private getRobotsContent(noindex?: boolean, nofollow?: boolean): string {
    const index = noindex ? 'noindex' : 'index';
    const follow = nofollow ? 'nofollow' : 'follow';
    return `${index}, ${follow}`;
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(
    breadcrumbs: Array<{ name: string; url: string }>,
  ): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: `${this.baseUrl}${item.url}`,
      })),
    };
  }

  /**
   * Generate website structured data
   */
  generateWebsiteSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.siteName,
      url: this.baseUrl,
      description:
        'Website đọc truyện chữ online miễn phí với kho tàng truyện chữ phong phú, cập nhật liên tục',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${this.baseUrl}/tim-kiem?q={search_term_string}`,
        'query-input': 'required name=search_term_string',
      },
      publisher: {
        '@type': 'Organization',
        name: this.siteName,
        url: this.baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${this.baseUrl}/logo.png`,
          width: 480,
          height: 480,
        },
      },
    };
  }

  /**
   * Generate novel book structured data
   */
  generateNovelSchema(novel: Novel): any {
    // Enhanced description with strategic keyword placement
    const genreList =
      novel.genres?.map((g) => g.title).join(', ') || 'đa thể loại';
    const statusText = novel.status === 1 ? 'hoàn thành' : 'đang cập nhật';

    const enhancedDescription = `Đọc truyện ${novel.title} ${novel.author ? `của tác giả ${novel.author}` : ''} online miễn phí tại website số 1 Việt Nam. Truyện ${genreList} ${statusText} với ${novel.numChapter || 0} chương, cập nhật nhanh nhất, chất lượng cao, không quảng cáo. ${novel.title} thuộc top truyện ${genreList} hay nhất, đọc mượt mà trên mọi thiết bị, hỗ trợ offline. Theo dõi ngay để không bỏ lỡ chương mới!`;

    const schema: any = {
      '@context': 'https://schema.org',
      '@type': 'Book',
      name: novel.title,
      description: enhancedDescription,
      image: novel.coverImage || `${this.baseUrl}${this.defaultImage}`,
      url: this.getFullNovelUrl(novel),
      identifier: {
        '@type': 'PropertyValue',
        name: 'Novel ID',
        value: novel.id.toString(),
      },
      author: this.generateAuthorObject(novel.author),
      genre: novel.genres?.map((g) => g.title) || [],
      bookFormat: 'EBook',
      inLanguage: 'vi',
      publisher: {
        '@type': 'Organization',
        name: this.siteName,
        url: this.baseUrl,
      },
      datePublished: novel.updateAt,
      dateModified: novel.updateAt,
      numberOfPages: novel.numChapter,
      workExample:
        novel.chapters?.slice(0, 5).map((chapter: Chapter) => ({
          '@type': 'Chapter',
          name: chapter.title || `Chương ${chapter.slug}`,
          url: this.getFullChapterUrl(novel, chapter),
          position: chapter.slug || chapter.id,
        })) || [],
      audience: {
        '@type': 'Audience',
        audienceType: 'Novel Readers',
      },
      keywords: this.generateNovelKeywordsString(novel),
      wordCount: novel.wordCount || undefined,
    };

    return schema;
  }

  /**
   * Generate content rating based on genres
   */
  private getContentRating(genres: Genre[]): string {
    if (!genres) return 'General';
    const genreNames = genres.map((g) => g.title.toLowerCase());
    if (
      genreNames.some((name) =>
        ['adult', 'smut', 'mature', 'ecchi', '18+'].includes(name),
      )
    ) {
      return 'Mature';
    }
    if (
      genreNames.some((name) =>
        ['romance', 'drama', 'psychological'].includes(name),
      )
    ) {
      return 'Teen';
    }
    return 'General';
  }

  /**
   * Generate keywords string for novel
   */
  private generateNovelKeywordsString(novel: Novel): string {
    // Core novel keywords
    const coreKeywords = [
      novel.title,
      `đọc truyện ${novel.title} online miễn phí`,
      `truyện ${novel.title} full hoàn thành`,
      `${novel.title} chap mới nhất`,
    ];

    // Author-specific keywords
    const authorKeywords = novel.author
      ? [
          `tác giả ${novel.author}`,
          `truyện của ${novel.author}`,
          `${novel.author} viết gì hay`,
        ]
      : [];

    // Genre-specific long-tail keywords
    const genreKeywords: string[] = [];
    if (novel.genres && novel.genres.length > 0) {
      novel.genres.forEach((genre) => {
        genreKeywords.push(
          `truyện ${genre.title} hay nhất`,
          `đọc ${genre.title} online`,
          `${genre.title} full không quảng cáo`,
          `top truyện ${genre.title} 2025`,
        );
      });
    }

    // Status-based keywords
    const statusKeywords =
      novel.status === 1
        ? ['truyện hoàn thành', 'full hết', 'đã end', 'truyện xong rồi']
        : ['đang cập nhật', 'chap mới', 'theo dõi cập nhật', 'chưa hoàn thành'];

    // Reading experience keywords
    const experienceKeywords = [
      'đọc truyện không lag',
      'giao diện đẹp',
      'đọc offline được',
      'truyện chất lượng cao',
      'cập nhật nhanh',
      'dịch chuẩn',
    ];

    // Combine all keywords with priority order
    const allKeywords = [
      ...coreKeywords,
      ...authorKeywords,
      ...genreKeywords.slice(0, 8), // Limit genre keywords
      ...statusKeywords,
      ...experienceKeywords.slice(0, 4), // Limit experience keywords
    ];

    return allKeywords.filter(Boolean).join(', ');
  }

  /**
   * Generate article structured data for chapters
   */
  generateChapterSchema(novel: Novel, chapter: Chapter): any {
    const chapterUrl = this.getFullChapterUrl(novel, chapter);
    const chapterTitle = `Chương ${chapter.slug}`;
    const description = `${chapterTitle}: Đọc truyện "${novel.title}"${novel.author ? ' của ' + novel.author : ''}. Nội dung mới nhất, chất lượng, cập nhật liên tục tại ${globalConfig.APP_NAME}.`;
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: `${novel.title} ${chapterTitle}`,
      alternativeHeadline: `Đọc ${novel.title} ${chapterTitle} Online Miễn Phí`,
      description: description,
      image: novel.coverImage || `${this.baseUrl}${this.defaultImage}`,
      url: chapterUrl,
      datePublished: chapter.updateAt,
      dateModified: chapter.updateAt,
      author: this.generateAuthorObject(novel.author),
      publisher: {
        '@type': 'Organization',
        name: this.siteName,
        url: this.baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${this.baseUrl}/logo.png`,
          width: 480,
          height: 480,
        },
      },
      mainEntity: {
        '@type': 'Chapter',
        name: `${novel.title} ${chapterTitle}`,
        url: chapterUrl,
      },
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': chapterUrl,
      },
      isPartOf: {
        '@type': 'Book',
        name: novel.title,
        url: this.getFullNovelUrl(novel),
      },
      position: chapter.slug,
      inLanguage: 'vi',
      genre: novel.genres?.map((g) => g.title) || [],
      keywords: this.generateChapterKeywords(novel, chapter),
    };
  }

  /**
   * Generate chapter-specific keywords
   */
  private generateChapterKeywords(novel: Novel, chapter: Chapter): string {
    const chapterName = chapter.title || `Chương ${chapter.slug}`;

    // Core chapter keywords with high search volume
    const coreKeywords = [
      `${novel.title} ${chapterName}`,
      `đọc ${novel.title} chương ${chapter.slug} online`,
      `${novel.title} chap ${chapter.slug} miễn phí`,
      `truyện ${novel.title} ${chapterName} full`,
    ];

    // Reading action keywords
    const actionKeywords = [
      `xem ${novel.title} ${chapterName}`,
      `${novel.title} ${chapterName} vietsub`,
      `${novel.title} chương mới nhất`,
      `cập nhật ${novel.title} hôm nay`,
    ];

    // Author and genre context
    const contextKeywords: string[] = [];
    if (novel.author) {
      contextKeywords.push(
        `${novel.author} ${novel.title} ${chapterName}`,
        `tác giả ${novel.author} viết`,
      );
    }

    if (novel.genres && novel.genres.length > 0) {
      const primaryGenre = novel.genres[0].title;
      contextKeywords.push(
        `truyện ${primaryGenre} ${novel.title}`,
        `${primaryGenre} hay nhất`,
      );
    }

    // Chapter-specific long-tail keywords
    const chapterSpecific = [
      `${novel.title} chương ${chapter.slug} review`,
      `nội dung ${novel.title} ${chapterName}`,
      `spoiler ${novel.title} chap ${chapter.slug}`,
      `${novel.title} ${chapterName} thảo luận`,
    ];

    // Quality and experience keywords
    const qualityKeywords = [
      'dịch chất lượng cao',
      'đọc mượt không quảng cáo',
      'cập nhật nhanh nhất',
      'full HD text',
    ];

    // Combine with strategic ordering
    const allKeywords = [
      ...coreKeywords,
      ...actionKeywords,
      ...contextKeywords.slice(0, 3),
      ...chapterSpecific.slice(0, 2),
      ...qualityKeywords.slice(0, 2),
    ];

    return allKeywords.filter(Boolean).join(', ');
  }

  /**
   * Generate ItemList schema for novel listings
   */
  generateNovelListSchema(
    novels: Novel[],
    listName: string,
    listDescription?: string,
  ): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      name: listName,
      description:
        listDescription ||
        `Danh sách ${listName.toLowerCase()} tại ${this.siteName}`,
      numberOfItems: novels.length,
      itemListOrder: 'https://schema.org/ItemListOrderDescending',
      itemListElement: novels.map((novel, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Book',
          name: novel.title,
          url: this.getFullNovelUrl(novel),
          image: novel.coverImage,
          author: this.generateAuthorObject(novel.author),
          genre: novel.genres?.map((g) => g.title) || [],
          dateModified: novel.updateAt,
        },
      })),
    };
  }

  /**
   * Generate FAQ schema for novel pages
   */
  generateNovelFAQSchema(novel: Novel): any {
    const latestChapter = novel.chapters?.[0]?.slug;
    const status = novel.status === 0 ? 'Đang cập nhật' : 'Hoàn thành';
    const totalChapters = novel.numChapter;

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: `${novel.title} có bao nhiêu chương?`,
          acceptedAnswer: {
            '@type': 'Answer',
            text: `${novel.title} hiện tại có ${totalChapters} chương, chương mới nhất là chương ${latestChapter}.`,
          },
        },
        {
          '@type': 'Question',
          name: `Tình trạng của ${novel.title} như thế nào?`,
          acceptedAnswer: {
            '@type': 'Answer',
            text: `${novel.title} hiện tại đang ở trạng thái ${status}.`,
          },
        },
        {
          '@type': 'Question',
          name: `Đọc ${novel.title} ở đâu miễn phí?`,
          acceptedAnswer: {
            '@type': 'Answer',
            text: `Bạn có thể đọc ${novel.title} miễn phí tại ${this.siteName} với chất lượng cao và cập nhật nhanh nhất.`,
          },
        },
        {
          '@type': 'Question',
          name: `${novel.title} thuộc thể loại gì?`,
          acceptedAnswer: {
            '@type': 'Answer',
            text: `${novel.title} thuộc thể loại ${novel.genres?.map((g) => g.title).join(', ') || 'Đang cập nhật'}.`,
          },
        },
      ],
    };
  }

  /**
   * Generate Organization schema for the website
   */
  generateOrganizationSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: this.siteName,
      url: this.baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${this.baseUrl}/logo.png`,
        width: 480,
        height: 480,
      },
      description:
        'Website đọc truyện chữ online hàng đầu Việt Nam với kho tàng truyện chữ phong phú',
      foundingDate: '2025',
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        availableLanguage: 'Vietnamese',
      },
      areaServed: 'VN',
      serviceType: 'Online Novel Reading Platform',
    };
  }
}
