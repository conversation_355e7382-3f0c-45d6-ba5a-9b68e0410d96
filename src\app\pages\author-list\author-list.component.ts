import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { ImgLayoutComponent } from "@layouts/img-layout/img-layout.component";

export interface Author {
  name: string;
  slug: string;
  novelCount: number;
  totalViews: number;
  totalFollowers: number;
  avgRating: number;
  avatar?: string;
  description?: string;
  popularNovels: string[];
}

@Component({
  selector: 'main[app-author-list]',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    BreadcrumbComponent,
    PaginationComponent,
    ImgLayoutComponent
],
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
})
export class AuthorListComponent implements OnInit {
  authors: Author[] = [];
  isLoading = true;
  searchQuery = '';
  filteredAuthors: <AUTHORS>
  currentPage = 1;
  itemsPerPage = 24;
  totalPages = 1;

  constructor(
    private router: Router,
    private seoService: SeoService,
  ) {}

  ngOnInit(): void {
    this.setupSEO();
    this.loadAuthors();
  }

  private setupSEO(): void {
    this.seoService.setSEOData({
      title: 'Danh sách tác giả - SayTruyenHot',
      description:
        'Khám phá danh sách các tác giả nổi tiếng với những tác phẩm hay nhất. Tìm hiểu về các tác giả yêu thích và đọc truyện của họ.',
      keywords:
        'danh sách tác giả, tác giả nổi tiếng, tác giả truyện hay, tác giả Việt Nam, light novel author',
      url: 'https://saytruyenhot.com/tac-gia',
      type: 'website',
    });
  }

  private loadAuthors(): void {
    this.isLoading = true;

    // Tạo dữ liệu giả lập cho danh sách tác giả
    const mockAuthors: <AUTHORS>
      {
        name: 'Nguyễn Văn A',
        slug: 'nguyen-van-a',
        novelCount: 15,
        totalViews: 2500000,
        totalFollowers: 85000,
        avgRating: 4.6,
        avatar: '/assets/author-avatars/author1.jpg',
        description: 'Tác giả chuyên viết tiểu thuyết kiếm hiệp và tiên hiệp',
        popularNovels: [
          'Kiếm Thần Truyền Kỳ',
          'Tu Tiên Đại Đạo',
          'Võ Lâm Truyền Thuyết',
        ],
      },
      {
        name: 'Trần Thị B',
        slug: 'tran-thi-b',
        novelCount: 12,
        totalViews: 1800000,
        totalFollowers: 62000,
        avgRating: 4.4,
        avatar: '/assets/author-avatars/author2.jpg',
        description: 'Chuyên gia viết truyện ngôn tình và lãng mạn',
        popularNovels: [
          'Tình Yêu Không Biên Giới',
          'CEO Lạnh Lùng',
          'Mối Tình Đầu',
        ],
      },
      {
        name: 'Lê Văn C',
        slug: 'le-van-c',
        novelCount: 8,
        totalViews: 1200000,
        totalFollowers: 45000,
        avgRating: 4.2,
        avatar: '/assets/author-avatars/author3.jpg',
        description: 'Tác giả của những câu chuyện đô thị hiện đại',
        popularNovels: [
          'Thành Phố Không Ngủ',
          'Doanh Nhân Trẻ',
          'Cuộc Sống Đô Thị',
        ],
      },
      {
        name: 'Phạm Thị D',
        slug: 'pham-thi-d',
        novelCount: 20,
        totalViews: 3200000,
        totalFollowers: 120000,
        avgRating: 4.8,
        avatar: '/assets/author-avatars/author4.jpg',
        description: 'Nữ hoàng của thể loại cung đấu và cổ trang',
        popularNovels: [
          'Hoàng Hậu Quyền Mưu',
          'Cung Đấu Truyền Kỳ',
          'Phi Tần Tranh Sủng',
        ],
      },
      {
        name: 'Hoàng Văn E',
        slug: 'hoang-van-e',
        novelCount: 6,
        totalViews: 950000,
        totalFollowers: 38000,
        avgRating: 4.1,
        avatar: '/assets/author-avatars/author5.jpg',
        description: 'Chuyên viết khoa học viễn tưởng và fantasy',
        popularNovels: ['Vũ Trụ Bí Ẩn', 'Robot Tương Lai', 'Thế Giới Ảo'],
      },
      {
        name: 'Vũ Thị F',
        slug: 'vu-thi-f',
        novelCount: 14,
        totalViews: 2100000,
        totalFollowers: 75000,
        avgRating: 4.5,
        avatar: '/assets/author-avatars/author6.jpg',
        description: 'Tác giả truyện trinh thám và bí ẩn',
        popularNovels: ['Thám Tử Bí Ẩn', 'Vụ Án Ly Kỳ', 'Bí Mật Đen Tối'],
      },
      {
        name: 'Đặng Văn G',
        slug: 'dang-van-g',
        novelCount: 18,
        totalViews: 2800000,
        totalFollowers: 95000,
        avgRating: 4.7,
        avatar: '/assets/author-avatars/author7.jpg',
        description: 'Master của thể loại huyền huyễn và ma thuật',
        popularNovels: [
          'Pháp Sư Vĩ Đại',
          'Thế Giới Ma Thuật',
          'Học Viện Phép Thuật',
        ],
      },
      {
        name: 'Bùi Thị H',
        slug: 'bui-thi-h',
        novelCount: 11,
        totalViews: 1650000,
        totalFollowers: 58000,
        avgRating: 4.3,
        avatar: '/assets/author-avatars/author8.jpg',
        description: 'Chuyên viết truyện học đường và thanh xuân',
        popularNovels: ['Ký Ức Học Trò', 'Tình Bạn Tuổi 17', 'Ngày Xưa Ơi'],
      },
      {
        name: 'Ngô Văn I',
        slug: 'ngo-van-i',
        novelCount: 9,
        totalViews: 1350000,
        totalFollowers: 48000,
        avgRating: 4.0,
        avatar: '/assets/author-avatars/author9.jpg',
        description: 'Tác giả truyện phiêu lưu và mạo hiểm',
        popularNovels: [
          'Hành Trình Bí Ẩn',
          'Kho Báu Cướp Biển',
          'Thám Hiểm Rừng Rậm',
        ],
      },
      {
        name: 'Lý Thị K',
        slug: 'ly-thi-k',
        novelCount: 16,
        totalViews: 2400000,
        totalFollowers: 82000,
        avgRating: 4.6,
        avatar: '/assets/author-avatars/author10.jpg',
        description: 'Chuyên gia truyện kinh dị và supernatural',
        popularNovels: [
          'Linh Hồn Bóng Tối',
          'Ma Quỷ Giữa Đêm',
          'Căn Nhà Bí Ẩn',
        ],
      },
    ];

    // Mở rộng danh sách để có nhiều tác giả hơn
    const expandedAuthors: <AUTHORS>
    for (let i = 0; i < 50; i++) {
      const baseAuthor = mockAuthors[i % mockAuthors.length];
      expandedAuthors.push({
        ...baseAuthor,
        name: `${baseAuthor.name} ${i + 1}`,
        slug: `${baseAuthor.slug}-${i + 1}`,
        novelCount: Math.floor(Math.random() * 20) + 5,
        totalViews: Math.floor(Math.random() * 3000000) + 500000,
        totalFollowers: Math.floor(Math.random() * 100000) + 20000,
        avgRating: +(Math.random() * 2 + 3).toFixed(1), // 3.0 - 5.0
      });
    }

    this.authors = expandedAuthors;
    this.filteredAuthors = [...this.authors];
    this.calculatePagination();
    this.isLoading = false;
  }

  onSearch(): void {
    if (!this.searchQuery.trim()) {
      this.filteredAuthors = [...this.authors];
    } else {
      this.filteredAuthors = this.authors.filter(
        (author) =>
          author.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          author.description
            ?.toLowerCase()
            .includes(this.searchQuery.toLowerCase()) ||
          author.popularNovels.some((novel) =>
            novel.toLowerCase().includes(this.searchQuery.toLowerCase()),
          ),
      );
    }
    this.currentPage = 1;
    this.calculatePagination();
  }

  private calculatePagination(): void {
    this.totalPages = Math.ceil(
      this.filteredAuthors.length / this.itemsPerPage,
    );
  }

  get paginatedAuthors(): Author[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredAuthors.slice(startIndex, endIndex);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  onAuthorClick(author: Author): void {
    this.router.navigate(['/tac-gia', author.slug]);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
      stars.push('full');
    }

    if (hasHalfStar) {
      stars.push('half');
    }

    while (stars.length < 5) {
      stars.push('empty');
    }

    return stars;
  }
}
