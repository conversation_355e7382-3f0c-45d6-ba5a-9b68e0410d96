import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { title } from 'process';


@Component({
  selector: '[app-img-layout]',
  standalone: true,
  templateUrl: './img-layout.component.html',
  styleUrl: './img-layout.component.scss',
  imports: [
    CommonModule
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host:
  {
    class: 'relative flex'
  }
})
export class ImgLayoutComponent {
  @Input()
  imageUrl?: string = 'https://static.saytruyenhot.com/coverimg/than-de-trong-sinh.jpg';
  @Input()
  title?: string  ;
  @Input()
  description?: string ;
  constructor(

  ) { }
  ngOnInit() {

  }
  ngAfterViewInit() {

  }

}
