<!-- Author List Page -->
<div
  app-img-layout
  [title]="'Danh Sách Tác Giả'"
  [description]="
    '<PERSON>h sách các tác giả những tác giả tài năng và những tác phẩm xuất sắc của họ'
  "
  class="relative flex min-h-screen"
>
  <!-- Breadcrumb -->
  <div
    app-breadcrumb
    class="flex py-2 mb-4"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Tác giả', url: '/tac-gia' },
    ]"
  ></div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-12">
    <div
      class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"
    ></div>
    <p class="mt-4 text-gray-600"><PERSON><PERSON> tả<PERSON> danh sách tác giả...</p>
  </div>

  <!-- Results Summary -->
  <div *ngIf="!isLoading" class="mb-6">
    <div class="bg-gray-50 dark:bg-dark-500 rounded-lg p-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="text-gray-700 dark:text-gray-300 mb-2 sm:mb-0">
          <span class="font-semibold">{{ filteredAuthors.length }}</span>
          tác giả được tìm thấy
          <span *ngIf="searchQuery" class="text-blue-600 ml-2"
            >cho "{{ searchQuery }}"</span
          >
        </div>
        <div *ngIf="searchQuery" class="text-sm">
          <button
            (click)="searchQuery = ''; onSearch()"
            class="text-blue-600 hover:text-blue-800 underline"
          >
            Xóa bộ lọc
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- Search Bar -->
  <div class="max-w-md mx-auto mb-4">
    <div class="relative">
      <input
        type="text"
        [(ngModel)]="searchQuery"
        (keyup.enter)="onSearch()"
        placeholder="Tìm kiếm tác giả..."
        class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-300"
      />
      <div
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <svg
          class="h-5 w-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          ></path>
        </svg>
      </div>
      <button
        (click)="onSearch()"
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-600 hover:text-blue-800"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 7l5 5-5 5M6 12h12"
          ></path>
        </svg>
      </button>
    </div>
  </div>
  <!-- Authors Grid -->
  <div class="mt-6">
    <h3 class="text-xl font-semibold mb-4 flex items-center">
      <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      Danh sách các tác giả
    </h3>

    <div
      *ngIf="!isLoading && paginatedAuthors.length > 0"
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
    >
      <div
        *ngFor="let author of paginatedAuthors"
        (click)="onAuthorClick(author)"
        class="author-card group bg-white dark:bg-dark-600 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-dark-500 hover:border-primary-100 dark:hover:border-primary-100 cursor-pointer"
      >
        <!-- Author Avatar -->
        <div
          class="h-40 overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
        >
          <div
            class="w-20 h-20 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white font-bold text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300"
          >
            {{ author.name.charAt(0).toUpperCase() }}
          </div>
        </div>

        <!-- Author Info -->
        <div class="p-3">
          <h4
            class="font-semibold text-gray-900 dark:text-white mb-1 line-clamp-2 group-hover:text-primary-100 transition-colors"
          >
            {{ author.name }}
          </h4>

          <p class="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
            {{ author.description }}
          </p>

          <!-- Statistics -->
          <div class="grid grid-cols-2 gap-2 mb-2">
            <div class="text-center">
              <div class="text-lg font-bold text-primary-100">
                {{ author.novelCount }}
              </div>
              <div class="text-xs text-gray-500">Tác phẩm</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600">
                {{ formatNumber(author.totalViews) }}
              </div>
              <div class="text-xs text-gray-500">Lượt đọc</div>
            </div>
          </div>

          <!-- Rating -->
          <div
            class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-2"
          >
            <span class="flex items-center">
              <svg
                class="w-3 h-3 mr-1 text-yellow-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              {{ author.avgRating }}/5
            </span>
            <span class="flex items-center">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M9 12h3.75M9 15h3.75M9 18c0 .621-.504 1.125-1.125 1.125s-1.125-.504-1.125-1.125 .504-1.125 1.125-1.125S9 17.379 9 18zM10.5 21c4.142 0 7.5-3.358 7.5-7.5s-3.358-7.5-7.5-7.5S3 9.358 3 13.5 6.358 21 10.5 21z"
                />
              </svg>
              {{ formatNumber(author.totalFollowers) }}
            </span>
          </div>

          <!-- Popular Novels -->
          <div class="border-t pt-2">
            <div class="text-xs text-gray-600 dark:text-gray-400">
              <div
                *ngFor="let novel of author.popularNovels.slice(0, 2)"
                class="truncate"
              >
                • {{ novel }}
              </div>
              <div
                *ngIf="author.popularNovels.length > 2"
                class="text-blue-600"
              >
                +{{ author.popularNovels.length - 2 }} tác phẩm khác
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results -->
    <div
      *ngIf="!isLoading && paginatedAuthors.length === 0"
      class="text-center py-12"
    >
      <div class="text-gray-500 dark:text-gray-400">
        <svg
          class="w-16 h-16 mx-auto mb-4 opacity-50"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Không tìm thấy tác giả
        </h3>
        <p class="mt-1 text-gray-500 dark:text-gray-400">
          Thử thay đổi từ khóa tìm kiếm hoặc xóa bộ lọc.
        </p>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="!isLoading && totalPages > 1" class="mt-8">
    <div
      app-pagination
      [currentPage]="currentPage"
      [totalpage]="totalPages"
      [rootLink]="'/tac-gia'"
      (OnChange)="onPageChange($event)"
    ></div>
  </div>
</div>
