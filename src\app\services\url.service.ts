// tutorial.service.ts

import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import globalConfig from 'globalConfig';

@Injectable({ providedIn: 'root' })
export class UrlService {
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  get STATIC_URL(): string {
    if (isPlatformBrowser(this.platformId) && globalConfig.IS_PRODUCTION) {
      return window.location.origin + '/static';
    } else {
      return globalConfig.BASE_API_URL + '/static';
    }
  }

  get BASE_URL(): string {
    if (isPlatformBrowser(this.platformId) && globalConfig.IS_PRODUCTION) {
      return window.location.origin;
    } else {
      return globalConfig.BASE_URL;
    }
  }

  get API_URL(): string {
    if (isPlatformBrowser(this.platformId) && globalConfig.IS_PRODUCTION) {
      return window.location.origin + '/api';
    } else {
      return globalConfig.BASE_API_URL + '/api';
    }
  }

  getUrl(path: string): string {
    if (path.startsWith('/')) {
      return `${this.BASE_URL}${path}`;
    }
    return `${this.BASE_URL}/${path}`;
  }
}
