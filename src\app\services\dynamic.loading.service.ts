import {
  ComponentRef,
  Inject,
  Injectable,
  PLATFORM_ID,
  Renderer2,
  RendererFactory2,
  Type,
  ViewContainerRef,
} from '@angular/core';
import { ConfirmPopupComponent } from '@components/confirm-popup/confirm-popup.component';

@Injectable({
  providedIn: 'root',
})
export class DynamicLoadingService {
  private renderer?: Renderer2;

  private components = new Map<number, ComponentRef<any>>();
  private _viewContainerRef?: ViewContainerRef;
  set viewContainerRef(viewContainerRef: ViewContainerRef | undefined) {
    this._viewContainerRef = viewContainerRef;
  }
  constructor(
    rendererFactory: RendererFactory2,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {}

  public showConfirmPopup({
    title,
    message,
    confirmButtonText,
    cancelButtonText,
  }: {
    title?: string;
    message?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
  }) {
    let componentRef = this.createDynamicComponent<ConfirmPopupComponent>(
      ConfirmPopupComponent,
    );
    return componentRef?.instance.show({
      title,
      message,
      confirmButtonText,
      cancelButtonText,
    });
  }
  hashCode = function (s: string) {
    let h = 0;
    let l = s.length;
    let i = 0;
    if (l > 0) while (i < l) h = ((h << 5) - h + s.charCodeAt(i++)) | 0;
    return h;
  };

  public createDynamicComponent<T>(
    componentType: Type<T>,
    viewContainerRef: ViewContainerRef,
  ): ComponentRef<T>;
  public createDynamicComponent<T>(
    componentType: Type<T>,
  ): ComponentRef<T> | undefined;
  public createDynamicComponent<T>(
    componentType: Type<T>,
    viewContainerRef?: ViewContainerRef,
  ): ComponentRef<T> | undefined {
    // Case 1: viewContainerRef provided (simple case)
    if (viewContainerRef) {
      const componentRef = viewContainerRef.createComponent(componentType);
      return componentRef;
    }

    // Case 2: use internal _viewContainerRef with caching
    if (!this._viewContainerRef) return;

    const key = this.hashCode(componentType.toString());

    if (this.components.has(key)) {
      const componentRef = this.components.get(key) as ComponentRef<T>;
      if (!componentRef.hostView.destroyed) return componentRef;
      this.components.delete(key);
    }

    const componentRef = this._viewContainerRef.createComponent(componentType)!;
    this.components.set(key, componentRef);
    return componentRef;
  }

  public destroyDynamicComponent<T>(componentType: Type<T>) {
    let key = this.hashCode(componentType.toString());

    if (this.components.has(key)) {
      let componentRef = this.components.get(key) as ComponentRef<T>;
      if (!componentRef.hostView.destroyed) {
        this.components.delete(key);
      }
    }
  }
}
