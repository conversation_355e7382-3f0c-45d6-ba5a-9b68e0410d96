@keyframes animationHiddenFrame {
  0% {
    height: auto;
  }

  100% {
    height: 0%;
  }
}

.HiddenAnimation {
  animation: animationHiddenFrame 2s linear 2s infinite alternate;
}

.comment-block {
  @apply flex w-full;
}
.comment-avatar {
  @apply px-1 md:px-3;
}
.comment-avatar-icon {
  @apply flex-start size-12 cursor-pointer;
}
.comment-avatar-img {
  @apply object-cover size-12 rounded-full;
}
.comment-content {
  @apply text-sm lg:text-base w-[calc(100%-56px)] md:w-[calc(100%-72px)] flex flex-col bg-gray-100 dark:bg-dark-750 space-y-1 rounded-2xl leading-normal resize-none py-2 px-3 border border-gray-200 dark:border-gray-600;
}
.comment-header {
  @apply flex-center;
}
.comment-username {
  @apply font-semibold text-center text-sm;
}
.comment-date {
  @apply ml-2 text-gray-600 dark:text-gray-400 text-[0.75rem] font-thin;
}
.comment-chapter {
  @apply hover:underline hover:text-gray-500 dark:text-gray-400 text-[0.75rem] font-semibold text-gray-400;
}
.comment-text {
  @apply items-center leading-[35px] text-gray-600 dark:text-gray-100 text-sm break-words;
}
.comment-actions {
  @apply flex flex-row flex-nowrap items-center;
}
.comment-actions-left {
  @apply flex flex-row flex-nowrap justify-start items-center pr-2;
}
.comment-action-icon {
  @apply h-4 w-4;
}
.comment-action-text {
  @apply mx-1;
}
.comment-reply {
  @apply text-center ml-2 font-semibold text-[0.75rem] text-gray-500 hover:text-gray-800 dark:text-gray-100 dark:hover:text-gray-200;
}
.comment-view-reply {
  @apply text-right hover:underline text-sm text-primary-100;
}
