import { isPlatform<PERSON>rowser, Ng<PERSON>ty<PERSON> } from '@angular/common';
import {
  Component,
  Inject,
  Input,
  NgZone,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { timer } from 'rxjs';

@Component({
  selector: '[app-spinner]',
  templateUrl: './spinner.component.html',
  styleUrl: './spinner.component.scss',
})
export class SpinnerComponent implements OnInit {
  loading = true;
  @Input() sizeSpinner = '12';
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private ngZone: NgZone,
  ) {}
  ngOnInit() {
    this.loading = true;
    if (isPlatformBrowser(this.platformId)) {
      this.ngZone.runOutsideAngular(() => {
        timer(3000).subscribe(() => {
          this.loading = false;
        });
      });
    }
  }
}
