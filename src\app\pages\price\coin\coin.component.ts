import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FeatureFlags } from '@schemas/enum';
import { Item } from '@schemas/Item';
import { AccountService } from '@services/account.service';
import { FeatureFlagsService } from '@services/feature-flags.service';
import { PaymentService } from '@services/payment.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import globalConfig from 'globalConfig';

@Component({
  selector: '[app-coin]',
  imports: [CommonModule],
  templateUrl: './coin.component.html',
  styleUrl: './coin.component.scss',
})
export class CoinComponent {
  items: Item[] = [];
  appName = globalConfig.APP_NAME;

  constructor(
    private paymentService: PaymentService,
    private toastService: ToastService,
    private accountService: AccountService,
    private seoService: SeoService,
    private featureFlags: FeatureFlagsService,
  ) {
    this.setupSeo();
  }

  ngOnInit() {
    this.paymentService.getBuyItems().subscribe((res) => {
      if (res.status === 1) {
        this.items = res.data!.filter((item) => item.type === 'COIN');
      }
    });
  }

  buyItem(item: Item) {
    if (!this.featureFlags.isEnabled(FeatureFlags.FEATURE_COIN)) {
      this.toastService.show(
        ToastType.Success,
        `Tính năng mua xu đang phát triển`,
      );
      return;
    }

    this.paymentService.buyItem(item).subscribe((res) => {
      console.log(res);

      if (res.status === 1) {
        let user = this.accountService.GetUser();
        if (user) {
          user.coin = res.data.coin;
          this.toastService.show(
            ToastType.Success,
            `Bạn đã nạp thành công ${item.name} `,
          );
          this.accountService.SaveUser(user);
        }
      } else {
        this.toastService.show(
          ToastType.Error,
          `Đã có lỗi xảy ra vui lòng kiểm tra lại`,
        );
      }
    });
  }

  private setupSeo(): void {
    const title = `Nạp xu`;
    const description = `Nạp xu tại ${this.appName} để mở khóa các chương truyện premium. Mua xu dễ dàng, thanh toán an toàn và được nạp ngay lập tức vào tài khoản của bạn.`;
    const url = `${globalConfig.BASE_URL}/price/coin`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `nạp xu ${this.appName}, mua xu đọc truyện, xu premium, mở khóa chương truyện, thanh toán online, nạp tiền đọc truyện`,
      canonical: url,
      section: 'Dịch vụ xu',
      noindex: false,
      nofollow: false,
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateCoinServiceSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Bảng giá', url: '/price' },
        { name: 'Nạp xu', url: '/price/coin' },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateCoinServiceSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: `Dịch vụ nạp xu ${this.appName}`,
      description: `Nạp xu để mở khóa các chương truyện premium tại ${this.appName}`,
      url: `${globalConfig.BASE_URL}/price/coin`,
      provider: {
        '@type': 'Organization',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      serviceType: 'Virtual Currency',
      areaServed: 'VN',
      category: 'Entertainment',
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Gói xu',
        itemListElement: [
          {
            '@type': 'Offer',
            name: 'Xu premium',
            description: 'Mua xu để mở khóa các chương truyện premium',
          },
        ],
      },
      additionalProperty: [
        {
          '@type': 'PropertyValue',
          name: 'Nạp ngay lập tức',
          value: 'true',
        },
        {
          '@type': 'PropertyValue',
          name: 'Thanh toán an toàn',
          value: 'true',
        },
        {
          '@type': 'PropertyValue',
          name: 'Mở khóa chương premium',
          value: 'true',
        },
      ],
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Trang chủ',
            item: globalConfig.BASE_URL,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Bảng giá',
            item: `${globalConfig.BASE_URL}/price`,
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: 'Nạp xu',
            item: `${globalConfig.BASE_URL}/price/coin`,
          },
        ],
      },
    };
  }
}
