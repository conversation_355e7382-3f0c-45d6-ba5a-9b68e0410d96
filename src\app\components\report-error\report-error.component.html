<div
  *ngIf="isShow"
  [@openDialog]
  class="fixed inset-0 flex-center bg-neutral-800 bg-opacity-50 z-50"
>
  <div class="bg-white p-6 rounded-lg shadow-lg relative dark:bg-neutral-800">
    <button
      class="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
      (click)="cancelReportError()"
    >
      <svg
        class="h-5 w-5 text-gray-400"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <line x1="18" y1="6" x2="6" y2="18" />
        <line x1="6" y1="6" x2="18" y2="18" />
      </svg>
    </button>
    <div class="flex flex-col items-center mb-4">
      <svg
        class="size-6"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path
          d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
        />
        <line x1="12" y1="9" x2="12" y2="13" />
        <line x1="12" y1="17" x2="12.01" y2="17" />
      </svg>
      <h3 class="text-lg font-semibold mb-2 text-center w-full">
        Thông báo lỗi
      </h3>
    </div>
    <div>
      <form
        [formGroup]="errorForm"
        (ngSubmit)="sendReportError()"
        class="space-y-2"
      >
        <div class="flex flex-col">
          <label
            for="errorType"
            class="mb-1 font-medium text-sm flex justify-start"
            >Lỗi:</label
          >
          <input
            id="errorType"
            formControlName="errorType"
            type="text"
            maxlength="100"
            class="bg-gray-100 appearance-none border dark:bg-neutral-700 dark:text-white dark:border-neutral-600 dark:focus:border-primary-100 border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-primary-100"
          />

          <!-- Error message for required field -->
          <div
            *ngIf="
              errorForm.controls['errorType'].errors?.['required'] &&
              errorForm.controls['errorType'].touched
            "
            class="text-red-600 text-xs font-medium mt-1 flex justify-start"
          >
            Vui lòng nhập thông tin lỗi
          </div>
          <div
            *ngIf="
              errorForm.controls['errorType'].errors?.['maxlength'] &&
              errorForm.controls['errorType'].touched
            "
            class="text-red-600 text-xs font-thin mt-1 flex justify-start"
          >
            Không được vượt quá 1000 ký tự.
          </div>
        </div>
        <div
          id="tags"
          class="flex justify-start outline-primary-100 rounded-lg"
        >
          <div
            *ngFor="let tag of defaultType"
            (click)="setErrorType(tag)"
            class="flex m-2 rounded-lg appearance-none bg-gray-100 dark:bg-neutral-700 dark:text-white dark:border-neutral-600 dark:hover:border-primary-100 cursor-pointer border border-gray-200 hover:border-primary-100 font-medium text-sm px-2"
          >
            {{ tag }}
          </div>
        </div>
        <div class="flex flex-col justify-start">
          <label
            for="message"
            class="mb-1 font-medium text-sm flex justify-start"
            >Mô tả:</label
          >
          <input
            id="message"
            maxlength="1000"
            formControlName="message"
            type="text"
            class="bg-gray-100 dark:bg-neutral-700 dark:text-white dark:border-neutral-600 dark:focus:border-primary-100 appearance-none border border-gray-200 rounded w-full py-2 px-4 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-primary-100"
          />
          <div
            *ngIf="
              errorForm.controls['message'].errors?.['maxlength'] &&
              errorForm.controls['message'].touched
            "
            class="text-red-600 text-xs font-thin mt-1 flex justify-start"
          >
            Không được vượt quá 1000 ký tự.
          </div>
        </div>

        <div class="mt-5">
          <button
            class="bg-primary-100 hover:bg-primary-200 font-semibold text-white px-6 py-2 rounded-full mr-2 w-full mt-3"
            type="submit"
            [disabled]="!errorForm.valid"
          >
            Gửi
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
