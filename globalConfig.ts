class GlobalConfig {
  public BASE_API_URL: string;
  public BASE_CHAT_URL: string;
  public EnableCache: boolean;
  public APP_NAME: string;
  public BASE_URL: string;
  public HOST: string;
  public IS_PRODUCTION: boolean;

  constructor() {
    this.APP_NAME = 'SayTruyenHot';
    this.HOST = 'saytruyenhot.com';
    this.BASE_URL = `https://${this.HOST}`;
    // this.BASE_API_URL = 'http://localhost:5080';
    this.BASE_API_URL = 'http://***************:5080';
    this.BASE_CHAT_URL = 'http://localhost:8000/v1/chatbot';
    this.EnableCache = true;
    this.IS_PRODUCTION = false;
  }
  public GetDataHost() {}
}
let globalConfig = new GlobalConfig();
export default globalConfig;
