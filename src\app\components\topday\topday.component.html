<div class="top-day-container">
  <h2 class="top-day-title">TOP NGÀY</h2>

  <div class="px-2 my-2 overflow-hidden size-full max-w-72">
    <div
      #carousel
      class="relative flex-start h-full pb-[65%] select-none pointer-events-auto"
    >
      <div class="absolute z-10 top-5 left-0 right-0 bottom-10">
        <ng-container *ngFor="let item of items; let i = index">
          <div
            [ngStyle]="{
              left: item.left,
              perspective: '500px',
              display: item.hidden ? 'none' : 'flex',
              transform: 'scale(' + item.scale + ')',
            }"
            class="absolute top-0 h-full w-1/3 transition-all duration-500"
          >
            <div
              class="absolute top-0 right-0 left-0 bottom-0 size-full transition-transform duration-500 ease-in-out"
              (click)="Move(i - curIndex); this.clearInterval()"
              [ngStyle]="{
                transform: 'rotateY(' + item.rotatey + ')',
                transformOrigin: item.pivort,
                transformStyle: 'preserve-3d',
              }"
            >
              <div
                class="right-[0.5px] top-[1.5%] absolute h-[98.5%] w-4 bg-neutral-200 shadow-neutral-500 shadow-[inset_0_0_5px_2px] -z-10 transition-transform duration-500"
                style="transform-origin: right; transform: rotateY(-90deg)"
              ></div>
              <div
                class="left-[0.5px] top-0 absolute h-full w-4 bg-dark-800 -z-10 transition-transform duration-500"
                style="transform-origin: left; transform: rotateY(90deg)"
              ></div>

              <div
                class="topday-overlay"
                [ngClass]="{ 'bg-black/10': i !== curIndex }"
              ></div>

              <img
                class="object-cover size-full"
                [src]="item.image"
                onerror="this.src = 'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'"
                [alt]="item.title"
                loading="lazy"
              />

              <div
                class="flex-center absolute -bottom-3 right-1/2 translate-x-1/2 size-6 bg-black rounded-full text-bold text-white"
                [ngClass]="{ hidden: i !== curIndex }"
              >
                <span>{{ i + 1 }}</span>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div
      *ngFor="let novel of novels; let i = index"
      [ngClass]="{ 'opacity-100': i === curIndex, 'opacity-0': i !== curIndex }"
      class="absolute top-0 left-0 right-0 bottom-0 h-[55%] brightness-[0.5] transition-opacity duration-700 ease-in-out"
    >
      <img
        loading="lazy"
        class="object-cover object-center size-full rounded-t-2xl"
        [src]="novel.coverImage"
        (error)="
          novel.coverImage =
            'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'
        "
        [alt]="novel.title"
      />
    </div>
  </div>
  <div class="text-center flex items-center flex-col">
    <a
      [routerLink]="[
        '/truyen',
        novels[curIndex].url + '-' + novels[curIndex].id,
      ]"
      class="text-lg font-semibold line-clamp-1 mt-2 hover:text-primary-100"
    >
      {{ novels[curIndex].title }}
    </a>

    <a
      [routerLink]="['/tac-gia', novels[curIndex].author]"
      class="hover:underline line-clamp-1 text-gray-600 dark:text-dark-100"
    >
      {{ novels[curIndex].author }}
    </a>
    <span
      class="text-center text-sm text-gray-500 dark:text-dark-100 line-clamp-3 mt-2"
      [innerHTML]="
        novels[curIndex].description
          | defaultDescription: 2 : novels[curIndex].title : '3'
      "
    ></span>

    <a
      [routerLink]="[
        '/truyen',
        novels[curIndex].url + '-' + novels[curIndex].id,
      ]"
      class="mt-2 btn btn-primary !rounded-full"
    >
      <p class="font-bold ml-2 text-white">Đọc ngay</p>
      <svg
        class="size-6 text-white"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <line x1="17" y1="7" x2="7" y2="17" />
        <polyline points="8 7 17 7 17 16" />
      </svg>
    </a>
  </div>
</div>
