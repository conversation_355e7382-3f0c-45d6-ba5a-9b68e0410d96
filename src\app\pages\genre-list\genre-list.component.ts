import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  inject,
} from '@angular/core';
import { SeoService } from '@services/seo.service';
import { Genre } from '@schemas/Genre';
import { NovelService } from '@services/novel.service';
import { Observable } from 'rxjs';
import { UrlService } from '@services/url.service';

@Component({
  selector: 'main[app-genre-list]',
  templateUrl: './genre-list.component.html',
  styleUrls: ['./genre-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class GenreListComponent implements OnInit {
  private seoService = inject(SeoService);
  private novelService = inject(NovelService);
  private urlService = inject(UrlService);

  genres$: Observable<Genre[]>;

  constructor() {
    this.genres$ = this.novelService.getGenres();
  }

  ngOnInit(): void {
    this.setGenreListSEO();
  }

  private setGenreListSEO(): void {
    const seoData = {
      title: '<PERSON><PERSON>ách Thể Loại <PERSON>ruyện - SayTruyenHot',
      description:
        'Khám phá đầy đủ các thể loại truyện chữ online miễn phí tại SayTruyenHot. Từ tiên hiệp, ngôn tình, kiếm hiệp đến đô thị, huyền huyễn. Tìm thể loại truyện yêu thích của bạn.',
      url: this.urlService.getUrl('/the-loai'),
      image: 'https://static.saytruyenhot.com/public/logo.png',
    };

    this.seoService.setSEOData(seoData);

    // Breadcrumb structured data
    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Trang chủ',
          item: this.urlService.getUrl('/'),
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Thể loại',
          item: this.urlService.getUrl('/the-loai'),
        },
      ],
    };

    this.seoService.addStructuredData([breadcrumbSchema]);
  }

  trackByGenre(index: number, genre: Genre): number {
    return genre.id;
  }
}
