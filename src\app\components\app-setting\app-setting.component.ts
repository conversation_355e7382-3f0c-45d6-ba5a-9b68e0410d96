import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { ColorSelectComponent } from '@components/color-select/color-select.component';
import { DialogComponent } from '@components/dialog/dialog.component';
import { SelectComponent } from '@components/select/select.component';
import { SliderComponent } from '@components/slider/slider.component';
import { ToggleComponent } from '@components/toggle/toggle.component';
import { SettingOption } from '@schemas/SettingOption';
// import { themeOptions,settingOptions, fontOptions, lineHeightOptions, textureOptions } from '@components/utils/options';
import { SettingService } from '@services/setting.service';
import { PinesModule } from 'src/app/shared/pines/pines.module';

@Component({
  selector: '[app-app-setting]',
  standalone: true,
  templateUrl: './app-setting.component.html',
  styleUrl: './app-setting.component.scss',
  imports: [
    DialogComponent,
    CommonModule,
    SelectComponent,
    ToggleComponent,
    SliderComponent,
    ColorSelectComponent,
    PinesModule,
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppSettingComponent {
  GroupSetting: {
    label: string;
    value: number;
    settings?: SettingOption[];
    icon?: string;
  }[] = [
    {
      label: 'Giao diện',
      value: 1,
      icon: `<svg class="size-6"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  <path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1" />  <polygon points="12 15 17 21 7 21 12 15" /></svg>`,
    },
    {
      label: 'Đọc',
      value: 2,
      icon: `<svg class="size-6"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" /></svg>`,
    },
    {
      label: 'Nghe',
      value: 3,
      icon: `<svg class="size-6"  fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
</svg>
`,
    },
    {
      label: 'Tài khoản',
      value: 4,
      icon: `<svg class="size-6"  fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
</svg>
`,
    },
  ];
  selectedGroup = 1;
  constructor(public settingService: SettingService) {}
  ngOnInit() {
    for (let index = 0; index < this.GroupSetting.length; index++) {
      const element = this.GroupSetting[index];
      element.settings = this.settingService.GetSettingByGroup(index + 1);
    }
  }
  @Input() isVisible = false;
  public open(group = 1) {
    this.selectedGroup = group;
    this.isVisible = true;
  }

  public close() {
    this.isVisible = false;
  }

  selectOption(option: { label: string; value: number }) {
    this.selectedGroup = option.value;
  }

  OnSettingChange(settingOption: SettingOption, newValue: any) {
    this.settingService.Set(settingOption.type, newValue);
  }
}
