import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { AuthorComponent } from './author.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';

const routes: Routes = [
  {
    path: '',
    component: AuthorComponent,
  },
];

@NgModule({
  declarations: [AuthorComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    BreadcrumbComponent,
    NovelCardV2Component,
    ImgLayoutComponent,
  ],
})
export class AuthorModule {}
