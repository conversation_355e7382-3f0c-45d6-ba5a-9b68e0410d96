<div app-dialog [(isVisible)]="isVisible">
  <div
    class="bg-white text-current size-full sm:w-96 shadow rounded-lg dark:bg-neutral-800 dark:border dark:border-neutral-600"
  >
    <div class="py-8 px-4 sm:px-10">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 class="mt-2 text-center text-3xl font-bold dark:text-white">
          Đ<PERSON>ng nhập
        </h2>
        <p
          class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400 max-w"
        >
          hoặc
          <a
            class="font-medium text-primary-100 hover:underline cursor-pointer"
            (click)="register()"
          >
            Tạo tài k<PERSON>n
          </a>
        </p>
      </div>
      <form class="space-y-6 mt-10" (submit)="onSubmit()" [formGroup]="form">
        <div *ngFor="let control of ['email', 'password']">
          <label [for]="control" class="form-label">
            {{ control | titlecase }}
          </label>
          <div class="mt-1 relative">
            <input
              [id]="control"
              [name]="control"
              [type]="
                control === 'password' && !showPassword ? 'password' : 'text'
              "
              [formControlName]="control"
              [placeholder]="
                'Nhập ' + (control === 'email' ? 'địa chỉ email' : 'mật khẩu')
              "
              class="form-input"
              autocomplete="on"
              required
            />
            <button
              *ngIf="control === 'password'"
              type="button"
              (click)="showPassword = !showPassword"
              class="absolute inset-y-0 right-0 flex-start px-3 text-gray-500"
            ></button>
          </div>
          <div *ngIf="isControlInvalid(control)">
            <small
              *ngIf="form.get(control)?.hasError('required')"
              class="text-red-500"
            >
              Vui lòng nhập
              {{ control === "email" ? "địa chỉ email" : "password" }}
            </small>
            <small
              *ngIf="
                control === 'email' &&
                form.get(control)?.hasError('email') &&
                submitted
              "
              class="text-red-500"
            >
              Định dạng mail không hợp lệ
            </small>
          </div>
        </div>

        <div class="flex-between">
          <div class="flex-start">
            <input
              id="remember_me"
              name="remember_me"
              formControlName="remember"
              type="checkbox"
              class="dark:bg-dark-bg h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label
              for="remember_me"
              class="ml-2 block text-sm dark:text-gray-500"
            >
              Ghi nhớ mật khẩu
            </label>
          </div>

          <div class="text-sm">
            <a
              [routerLink]="['/auth/forgot-password']"
              class="font-medium text-primary-100 hover:underline"
            >
              Quên mật khẩu?
            </a>
          </div>
        </div>

        <div>
          <button
            type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-50"
            aria-label="Đăng nhập"
          >
            Đăng nhập
          </button>
        </div>
      </form>
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex-start">
            <div
              class="w-full border-t border-gray-300 dark:border-gray-600"
            ></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span
              class="px-2 bg-white text-gray-500 dark:bg-neutral-800 dark:text-gray-400"
            >
              Hoặc</span
            >
          </div>
        </div>

        <div class="mt-6 flex justify-center">
          <div class="w-full h-10 flex justify-center">
            <asl-google-signin-button
              type="standard"
              size="large"
              shape="rectangular"
              theme="filled_black"
              [width]="200"
            >
            </asl-google-signin-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
