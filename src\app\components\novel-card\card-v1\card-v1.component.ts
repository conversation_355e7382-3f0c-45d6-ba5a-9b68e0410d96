import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewEncapsulation,
} from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { Novel } from '@schemas/Novel';
import { EventService } from '@services/event.service';
// import { DateAgoPipe } from 'src/app/shared/pines/date-ago.pine';

@Component({
  selector: '[app-card-v1]',
  templateUrl: './card-v1.component.html',
  imports: [CommonModule, RouterLink],
  styleUrl: './card-v1.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NovelCardV1Component {
  onButtonClick(novel: Novel) {
    if (!this.iconRightRef) {
      this.router.navigate(['/', 'truyen', novel.url + '-' + novel.id]);
    } else {
      this.iconClick.emit(novel);
    }
  }
  @Input() novel?: Novel;
  @Output() iconClick = new EventEmitter<Novel>();
  @ContentChild('iconRight') iconRightRef: TemplateRef<HTMLElement> | null =
    null;
  constructor(
    private router: Router,
    private eventService: EventService,
  ) {}

  onHoverNovel(hover: boolean) {
    if (hover) {
      this.eventService.OnShowComicDetail.next(this.novel);
    } else {
      this.eventService.OnShowComicDetail.next(undefined);
    }
  }

  ngOnDestroy() {
    this.eventService.OnShowComicDetail.next(undefined);
  }
}
