import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
// import { INotification, IServiceResponse, User, VoteInfo } from '@schema';
import { INotification } from '@schemas/Notification';
import { IServiceResponse } from '@schemas/ResponseType';
import { User } from '@schemas/User';
import { VoteInfo } from '@schemas/Vote';
import { BehaviorSubject, Observable } from 'rxjs';
import { StorageService } from './storage.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  public currentUser$: BehaviorSubject<User | undefined>;

  user?: User;

  constructor(
    private httpClient: HttpClient,
    private storageService: StorageService,
    private urlService: UrlService,
  ) {
    this.currentUser$ = new BehaviorSubject<User | undefined>(this.GetUser());
  }
  GetUser() {
    this.user = this.storageService.GetUserData();
    return this.user;
  }

  Follow(novelid: number, isFollow: boolean) {
    return this.httpClient.post<IServiceResponse<number>>(
      `${this.urlService.API_URL}/user/follow?novelid=${novelid}&follow=${isFollow}`,
      {},
    );
  }
  GetFollowedNovels(page = 1, size = 28) {
    const searchParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    }).toString();

    return this.httpClient.get(
      `${this.urlService.API_URL}/user/followed-novels?${searchParams}`,
    );
  }

  GetDownloadedNovels(page = 1, size = 28) {
    const searchParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    }).toString();

    return this.httpClient.get(
      `${this.urlService.API_URL}/user/downloaded-novels?${searchParams}`,
    );
  }

  AddComment(
    novelId: number,
    content: string,
    replyfromUser?: number,
    replyfromCmt?: number | null,
  ) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/comment`, {
      novelId,
      content,
      replyfromUser,
      replyfromCmt,
    });
  }
  getAuthorizationToken(): string | undefined {
    return this.GetUser()?.token;
  }
  isAuthenticated(): boolean {
    return !!this.GetUser();
  }

  GetUserInfo() {
    return this.httpClient.get(`${this.urlService.API_URL}/user/me`);
  }
  GetUserById(id: number) {
    return this.httpClient.get<IServiceResponse<User>>(
      `${this.urlService.API_URL}/user/${id}`,
    );
  }

  Login(email: string, password: string) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/auth/login?email=${email}&password=${password}`,
    );
  }
  LoginWithSocial(user: any) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/auth/social-login`,
      user,
    );
  }
  SendEmailConfirm(email: string, userid: number) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/auth/send-confirm-email?email=${email}&userId=${userid}`,
    );
  }
  GetCommentsByNovelId(novelId: number, page: number, step: number) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/comments/novel/${novelId}?page=${page}&size=${step}`,
    );
  }
  Logout() {
    this.storageService.RemoveUserData();
  }
  Register(name: string, email: string, password: string) {
    return this.httpClient.post(`${this.urlService.API_URL}/auth/register`, {
      name,
      email,
      password,
    });
  }
  ForgetPassword(email: string) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/auth/forgot?email=${email}`,
    );
  }
  UpdateAvatar(avatar: FormData) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/avatar`,
      avatar,
    );
  }
  SaveUser(user: User) {
    this.storageService.SetUserData(user);
    this.currentUser$.next(user);
  }
  GetUserDeconfirm() {
    return this.storageService.GetUserDeconfirm();
  }
  SaveUserDeconfirm(infoUser: any) {
    this.storageService.SetUserDeconfirm(infoUser);
  }
  GetRememberMeData() {
    return this.storageService.GetRememberMeData();
  }
  SetRememberMeData(rememberMeData: any) {
    this.storageService.SetRememberMeData(rememberMeData);
  }
  UpdateInfo(user: User) {
    return this.httpClient.post(`${this.urlService.API_URL}/user/update`, user);
  }
  UpdatePassword(newPassword: string) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/password`,
      newPassword,
    );
  }
  UpdateTypeLevel(typeLevel: number) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/typelevel/${typeLevel}`,
      {},
    );
  }
  UpdateMaxim(maxim: string | null) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/update/maxim?maxim=${maxim}`,
      {},
    );
  }

  getUserNotify() {
    return this.httpClient.get<IServiceResponse<INotification[]>>(
      `${this.urlService.API_URL}/user/notify`,
    );
  }
  updateUserNotify(idNotify: number | null, IsRead: boolean | null) {
    return this.httpClient.post(
      `${this.urlService.API_URL}/user/notify/update`,
      {
        ID: idNotify,
        IsRead: IsRead,
      },
    );
  }
  deleteUserNotify(idNotify: number | null) {
    return this.httpClient.delete(
      `${this.urlService.API_URL}/user/notify/delete/${idNotify}`,
    );
  }
  voteNovel(
    novelId: number,
    voteValue: number,
  ): Observable<IServiceResponse<VoteInfo>> {
    const url = `${this.urlService.API_URL}/user/vote/update`;
    const body = { novelId, voteValue };
    return this.httpClient.post(url, body) as Observable<
      IServiceResponse<VoteInfo>
    >;
  }

  getVoteInfo(novelId: number): Observable<IServiceResponse<VoteInfo>> {
    const endpoint = `${this.urlService.API_URL}/user/vote`;
    const params = `?novelid=${novelId}`;
    const url = `${endpoint}${params}`;

    return this.httpClient.get(url) as Observable<IServiceResponse<VoteInfo>>;
  }
  unvoteNovel(novelId: number): Observable<IServiceResponse<void>> {
    const apiUrl = `${this.urlService.API_URL}/user/vote/delete`;
    const queryParams = `?novelId=${novelId}`;
    const requestUrl = `${apiUrl}${queryParams}`;

    return this.httpClient.delete(requestUrl) as Observable<
      IServiceResponse<void>
    >;
  }

  downloadNovel(novelId: number) {
    return this.httpClient.get(
      `${this.urlService.API_URL}/user/download/novel/${novelId}`,
      {
        responseType: 'blob',
      },
    );
  }
}
