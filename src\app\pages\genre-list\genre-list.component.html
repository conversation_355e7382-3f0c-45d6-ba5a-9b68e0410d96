<div
  app-img-layout
  [title]="'<PERSON><PERSON> S<PERSON>ch Thể Loại Truyện'"
  [description]="
    'Với đầy đủ các thể loại truyện chữ online miễn phí tại SayTruyenHot. Từ tiên hiệp, ng<PERSON><PERSON> tình, kiế<PERSON> hiệp đến đô thị, huy<PERSON><PERSON> huy<PERSON>.'
  "
  class="relative flex"
>
  <div
    app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Thể loại', url: '/the-loai' },
    ]"
  ></div>

  <!-- Genre Grid -->
  <div class="mt-6">
    <div
      class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
      *ngIf="genres$ | async as genres"
    >
      <a
        *ngFor="let genre of genres; trackBy: trackByGenre"
        [routerLink]="['/the-loai', genre.slug]"
        class="genre-card group bg-white dark:bg-dark-600 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-dark-500 hover:border-primary-100 dark:hover:border-primary-100"
        [title]="'Xem truyện thể loại ' + genre.title"
      >
        <div class="flex flex-col h-full">
          <h3
            class="text-lg font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-primary-100 transition-colors"
          >
            {{ genre.title }}
          </h3>

          <p
            class="text-sm text-gray-600 dark:text-gray-400 mb-3 flex-grow line-clamp-3"
          >
            {{
              genre.description ||
                "Khám phá những tác phẩm hay nhất thuộc thể loại " + genre.title
            }}
          </p>

            <div
            class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400"
            >
            <span class="flex items-center">
              <!-- Book SVG Icon -->
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 5.75A2.25 2.25 0 016 3.5h12a2.25 2.25 0 012.25 2.25v14a.75.75 0 01-1.2.6L12 16.25l-7.05 4.1a.75.75 0 01-1.2-.6v-14z"/>
              </svg>
              {{ genre.quantity || 0 }} truyện
            </span>
            <span
              class="text-primary-100 group-hover:translate-x-1 transition-transform"
            >
              →
            </span>
            </div>
        </div>
      </a>
    </div>

    <!-- Loading State -->
    <div
      *ngIf="!(genres$ | async)"
      class="flex justify-center items-center py-12"
    >
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-100"
      ></div>
    </div>
  </div>

  <!-- SEO Content Section -->
  <div class="seo-content mt-12 px-4 sm:px-2">
    <h2 class="text-2xl font-semibold mb-4">
      Khám Phá Đa Dạng Thể Loại Truyện Chữ Online
    </h2>

    <div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">
      <p class="mb-4">
        <strong>SayTruyenHot</strong> tự hào mang đến cho bạn
        <strong>kho tàng truyện chữ đa dạng</strong> với hàng chục thể loại khác
        nhau. Từ những <strong>truyện tiên hiệp hấp dẫn</strong>,
        <strong>ngôn tình lãng mạn</strong> đến
        <strong>kiếm hiệp cổ điển</strong> và <strong>đô thị hiện đại</strong>,
        chúng tôi có tất cả cho mọi sở thích đọc.
      </p>

      <h3 class="text-lg font-semibold mt-6 mb-3">
        Tại Sao Nên Chọn Đọc Theo Thể Loại?
      </h3>
      <ul class="list-disc list-inside mb-4 space-y-1">
        <li>
          <strong>Tìm truyện phù hợp</strong> với sở thích cá nhân dễ dàng
        </li>
        <li><strong>Khám phá thể loại mới</strong> và mở rộng tầm đọc</li>
        <li><strong>Theo dõi xu hướng</strong> các thể loại hot nhất</li>
        <li>
          <strong>Đọc truyện có chất lượng</strong> được tuyển chọn kỹ lưỡng
        </li>
        <li>
          <strong>Cập nhật nhanh chóng</strong> các tác phẩm mới trong từng thể
          loại
        </li>
      </ul>

      <h3 class="text-lg font-semibold mt-6 mb-3">
        Thể Loại Truyện Phổ Biến Nhất
      </h3>
      <p class="mb-4">
        Các <strong>thể loại truyện được yêu thích nhất</strong> tại
        SayTruyenHot bao gồm: <strong>Tiên hiệp</strong> với những cuộc phiêu
        lưu tu tiên, <strong>Ngôn tình</strong> với câu chuyện tình yêu ngọt
        ngào, <strong>Huyền huyễn</strong> với thế giới kỳ ảo,
        <strong>Đô thị</strong> với cuộc sống hiện đại và
        <strong>Kiếm hiệp</strong> với võ công cao cường.
      </p>

      <h3 class="text-lg font-semibold mt-6 mb-3">
        Trải Nghiệm Đọc Truyện Tối Ưu
      </h3>
      <p class="mb-4">
        Mỗi thể loại truyện tại SayTruyenHot đều được
        <strong>phân loại chi tiết</strong>,
        <strong>cập nhật thường xuyên</strong> và
        <strong>sắp xếp theo độ phổ biến</strong>. Bạn có thể dễ dàng
        <strong>lọc theo trạng thái</strong>,
        <strong>sắp xếp theo lượt xem</strong> hoặc
        <strong>tìm kiếm truyện mới nhất</strong> trong từng thể loại.
      </p>
    </div>
  </div>
</div>
