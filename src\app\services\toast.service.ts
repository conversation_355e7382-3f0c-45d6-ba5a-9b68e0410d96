// src/app/toast.service.ts
import { ComponentRef, Injectable } from '@angular/core';
import { ToastComponent } from '@components/toast/toast.component';
import { DynamicLoadingService } from './dynamic.loading.service';
import { timer } from 'rxjs';

export enum ToastType {
  Success = 'success',
  Error = 'error',
  Info = 'info',
  Warning = 'warning',
}
interface Toast {
  id: number;
  type: ToastType;
  message: string;
  state: 'enter' | 'leave'; // Add state property
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private componentRef?: ComponentRef<ToastComponent>;
  toasts: Toast[] = [];
  private nextId = 0;

  constructor(private dynamicService: DynamicLoadingService) {}
  show(type: ToastType, message: string, duration = 3000): number {
    this.componentRef =
      this.dynamicService.createDynamicComponent<ToastComponent>(
        ToastComponent,
      );
    const id = this.nextId++;
    const toast: Toast = { id, message, type, state: 'enter' }; // Initialize with 'enter' state
    this.toasts.push(toast);
    timer(duration).subscribe(() => this.startLeaveAnimation(toast)); // Start leave animation after 5 seconds
    return id;
  }

  stop(toastId: number): void {
    this.toasts = this.toasts.filter((t) => t.id !== toastId);
  }

  startLeaveAnimation(toast: Toast): void {
    toast.state = 'leave';
    timer(300).subscribe(() => this.remove(toast)); // Remove toast after leave animation duration
  }

  remove(toast: Toast): void {
    this.toasts = this.toasts.filter((t) => t.id !== toast.id);
  }

  clear(): void {
    this.toasts = [];
  }
}
