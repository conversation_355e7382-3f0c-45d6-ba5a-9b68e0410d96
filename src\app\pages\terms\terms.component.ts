import { Component } from '@angular/core';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';
import { ImgLayoutComponent } from "@layouts/img-layout/img-layout.component";

@Component({
  selector: 'main[app-terms]',
  imports: [ImgLayoutComponent],
  templateUrl: './terms.component.html',
  styleUrl: './terms.component.scss',
})
export class TermsComponent {
  appName = globalConfig.APP_NAME;

  constructor(private seoService: SeoService) {
    this.setupSeo();
  }

  private setupSeo(): void {
    const title = `Điều khoản sử dụng`;
    const description = `Điều khoản và điều kiện sử dụng dịch vụ tại ${this.appName}. Quy định về quyền và nghĩa vụ của người dùng khi sử dụng website đọc truyện chữ online.`;
    const url = `${globalConfig.BASE_URL}/dieu-khoan`;

    const seoData = {
      title,
      description,
      type: 'article' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `điều khoản sử dụng ${this.appName}, quy định sử dụng, điều kiện dịch vụ, quyền và nghĩa vụ người dùng, chính sách sử dụng, terms of service`,
      canonical: url,
      publishedTime: new Date().toISOString(),
      modifiedTime: new Date().toISOString(),
      section: 'Chính sách',
      author: this.appName,
      noindex: false,
      nofollow: false,
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateTermsPageSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Điều khoản sử dụng', url: '/dieu-khoan' },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateTermsPageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: `Điều khoản sử dụng - ${this.appName}`,
      description: `Điều khoản và điều kiện sử dụng dịch vụ tại ${this.appName}`,
      url: `${globalConfig.BASE_URL}/dieu-khoan`,
      inLanguage: 'vi-VN',
      isPartOf: {
        '@type': 'WebSite',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      mainEntity: {
        '@type': 'Thing',
        name: 'Điều khoản sử dụng',
        description:
          'Quy định về quyền và nghĩa vụ của người dùng tại ' + this.appName,
      },
      about: {
        '@type': 'Thing',
        name: 'Điều khoản sử dụng',
        description: 'Quy định về quyền và nghĩa vụ của người dùng',
      },
      publisher: {
        '@type': 'Organization',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      datePublished: new Date().toISOString(),
      dateModified: new Date().toISOString(),
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Trang chủ',
            item: globalConfig.BASE_URL,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Điều khoản sử dụng',
            item: `${globalConfig.BASE_URL}/dieu-khoan`,
          },
        ],
      },
    };
  }
}
