<div
  app-img-layout
  [title]="'Tác giả ' + authorName"
  [description]="'Tác giả ' + authorName + ' với ' + totalNovels + ' tác phẩm'"
>
  <div
    app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Tác giả', url: '/tac-gia' },
      { label: authorName, url: '/tac-gia/' + authorName.toLowerCase() },
    ]"
  ></div>

  <!-- Author Info & Stats -->
  <div class="author-stats mb-6 p-4 bg-gray-50 dark:bg-dark-500 rounded-lg">
    <div class="flex flex-wrap items-center justify-between">
      <div class="author-info">
        <h2 class="text-xl font-semibold mb-2">{{ authorName }}</h2>
        <p class="text-gray-600 dark:text-gray-400">
          Tác g<PERSON><PERSON> với {{ totalNovels }} tác phẩm được yêu thích
        </p>
      </div>
      <div class="author-stats-numbers flex gap-6">
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-primary-100">
            {{ totalNovels }}
          </div>
          <div class="text-sm text-gray-500">Tác phẩm</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-primary-100">
            <!-- {{ novels.reduce((total, novel) => total + novel.viewCount, 0) | number }} -->
          </div>
          <div class="text-sm text-gray-500">Lượt xem</div>
        </div>
        <div class="stat-item text-center">
          <div class="text-2xl font-bold text-primary-100">
            <!-- {{ (novels.reduce((total, novel) => total + novel.rating, 0) / novels.length || 0).toFixed(1) }} -->
          </div>
          <div class="text-sm text-gray-500">Đánh giá TB</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Novels Grid -->
  <div class="mt-6">
    <h2 class="text-xl font-semibold mb-4 flex items-center">
      <svg
        viewBox="-1.2 -1.2 26.40 26.40"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
        class="size-6"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier"></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M3 2a1 1 0 1 0-2 0v20a1 1 0 1 0 2 0V2ZM5 6.6A2.6 2.6 0 0 1 7.6 4h12.8A2.6 2.6 0 0 1 23 6.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 8.4V6.6ZM5 15.6A2.6 2.6 0 0 1 7.6 13h4.8a2.6 2.6 0 0 1 2.6 2.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 17.4v-1.8Z"
          ></path>
        </g>
      </svg>
      Tác phẩm của {{ authorName }}
    </h2>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
      *ngIf="!isLoading; else loadingTemplate"
    >
      <div
        app-card-v2
        *ngFor="let novel of novels; trackBy: trackByNovel"
        [novel]="novel"
      ></div>
    </div>

    <!-- Loading State -->
    <ng-template #loadingTemplate>
      <div class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-100"
        ></div>
      </div>
    </ng-template>

    <!-- Empty State -->
    <div *ngIf="!isLoading && novels.length === 0" class="text-center py-12">
      <div class="text-gray-500 dark:text-gray-400">
        <svg
          class="w-16 h-16 mx-auto mb-4 opacity-50"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-lg font-medium">Chưa có tác phẩm nào</p>
        <p class="text-sm">
          Tác giả {{ authorName }} chưa có tác phẩm nào trên hệ thống.
        </p>
      </div>
    </div>
  </div>
</div>
