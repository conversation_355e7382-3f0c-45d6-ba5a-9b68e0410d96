import {
  Component,
  Inject,
  OnInit,
  Optional,
  PLATFORM_ID,
  RESPONSE_INIT,
} from '@angular/core';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';

@Component({
  selector: 'main[app-not-found]',
  templateUrl: './not-found.component.html',
  styleUrl: './not-found.component.scss',
  standalone: false,
})
export class NotFoundComponent implements OnInit {
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    @Optional() @Inject(RESPONSE_INIT) private response: ResponseInit,
    private seoService: SeoService,
  ) {
    this.setupSeo();
  }

  ngOnInit() {
    if (this.response) {
      this.response.status = 404;
    }
  }

  private setupSeo() {
    const title = `Trang Không Tồn Tại - 404`;
    const description = `Trang bạn tìm kiếm không tồn tại hoặc đã bị xóa. Quay lại trang chủ để khám phá hàng ngàn truyện chữ hay tại ${globalConfig.APP_NAME}`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      siteName: globalConfig.APP_NAME,
      noindex: true,
      nofollow: false,
    };

    this.seoService.setSEOData(seoData);

    // Add 404 error structured data
    const errorSchema = {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: title,
      description: description,
      url: globalConfig.BASE_URL,
      mainEntity: {
        '@type': 'Thing',
        name: 'Page Not Found',
        description: 'The requested page could not be found',
      },
    };

    this.seoService.addStructuredData([errorSchema]);
  }
}
