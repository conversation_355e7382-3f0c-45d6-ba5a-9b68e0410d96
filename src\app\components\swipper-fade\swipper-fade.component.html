<div class="swiper-fade-container">
  <div class="swiper-fade-container2">
    <ng-container *ngFor="let item of items; let i = index">
      <div
        class="swiper-fade-item"
        [@fadeBg]="attrs[i].hidden ? 'hidden' : 'visible'"
      >
        <ng-template
          [ngTemplateOutlet]="bgTemplate || null"
          [ngTemplateOutletContext]="{ $implicit: i }"
        ></ng-template>
      </div>
      <div
        class="swiper-fade-bg"
        [@fadeMain]="attrs[i].hidden ? 'hidden' : 'visible'"
        [ngClass]="{ 'pointer-events-none': attrs[i].hidden }"
      >
        <ng-template
          [ngTemplateOutlet]="itemTemplate || null"
          [ngTemplateOutletContext]="{ $implicit: item }"
        ></ng-template>
      </div>
    </ng-container>
  </div>
  <ng-container>
    <ng-template
      [ngTemplateOutlet]="navTemplate || null"
      [ngTemplateOutletContext]="{ $implicit: index }"
    ></ng-template>
  </ng-container>
</div>
<ng-content />
