// Author List Component Styles

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.author-card {
  &:hover {
    .author-avatar {
      transform: scale(1.1);
    }
  }
}

.author-avatar {
  transition: transform 0.3s ease;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }
}

// Animation for loading
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.author-card {
  animation: fadeIn 0.5s ease-out;
}

// Search input enhancement
.search-input {
  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

// Button hover effects
.btn-primary {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// Card grid responsiveness
.authors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Pagination styling
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;

  .page-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background: white;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: #f9fafb;
    }

    &.active {
      background: #2563eb;
      color: white;
      border-color: #2563eb;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Statistics styling
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;

  .stat-item {
    text-align: center;

    .stat-value {
      font-size: 1.5rem;
      font-weight: bold;
      line-height: 1.2;
    }

    .stat-label {
      font-size: 0.75rem;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

// Rating stars
.rating-stars {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.125rem;

  .star {
    width: 1rem;
    height: 1rem;

    &.full {
      color: #fbbf24;
    }

    &.half {
      color: #fde68a;
    }

    &.empty {
      color: #d1d5db;
    }
  }

  .rating-value {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }
}

// Popular novels list
.popular-novels {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;

  .novels-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .novel-item {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;

    &:before {
      content: "•";
      margin-right: 0.5rem;
    }
  }

  .more-novels {
    font-size: 0.75rem;
    color: #2563eb;
    font-weight: 500;
  }
}

// Followers section
.followers-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 1rem;

  .followers-icon {
    width: 1rem;
    height: 1rem;
    color: #9ca3af;
    margin-right: 0.5rem;
  }

  .followers-text {
    font-size: 0.875rem;
    color: #6b7280;
  }
}
