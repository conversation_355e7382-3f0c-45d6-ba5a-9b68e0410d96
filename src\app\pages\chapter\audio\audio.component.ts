import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  NgZone,
  Output,
  SimpleChang<PERSON>,
  ViewChild,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { AppSettingComponent } from '@components/app-setting/app-setting.component';
import { SelectComponent } from '@components/select/select.component';
import { openDialog } from '@components/utils/animation';
import { BYTES_PER_SECOND } from '@components/utils/constants';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { ChapterPage } from '@schemas/ChapterPage';
import { SettingOption } from '@schemas/SettingOption';
import { SettingType } from '@schemas/SettingType.enum';
import { AccountService } from '@services/account.service';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { NovelService } from '@services/novel.service';
import { SettingService } from '@services/setting.service';
import { ToastService, ToastType } from '@services/toast.service';
import { interval, timer } from 'rxjs';

@Component({
  selector: '[app-audio]',
  imports: [CommonModule, SelectComponent, ClickOutsideDirective, RouterLink],
  templateUrl: './audio.component.html',
  styleUrl: './audio.component.scss',
  animations: [openDialog], // Add your animations here
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AudioComponent {
  public isVisible = false;
  public chapter?: ChapterPage;
  public volume = 1;
  public audioSpeed = 1;
  public voiceOptions?: SettingOption;
  public estimatedDuration = 0;
  public isStreaming = false;
  public autoZoomAudio = false;
  @ViewChild('audioPlayer') audioPlayer!: ElementRef<HTMLAudioElement>;
  isVisibleChange = new EventEmitter<boolean>();
  onEndAudioEvent = new EventEmitter<void>();
  fullmode = true;
  constructor(
    private novelService: NovelService,
    private toastservice: ToastService,
    private accountService: AccountService,
    private cd: ChangeDetectorRef,
    private dynamicLoadingService: DynamicLoadingService,
    private settingService: SettingService,
    private ngZone: NgZone,
  ) {}

  setChapterPage(chapter: ChapterPage) {
    this.chapter = chapter;
    this.estimatedDuration = 0;
    this.isStreaming = false;
    this.fullmode = true;
    if (this.audioPlayer) {
      this.audioPlayer.nativeElement.pause();
      this.audioPlayer.nativeElement.currentTime = 0;
    }
  }

  setVisible(visible: boolean) {
    this.isVisible = visible;
    if (visible) {
      timer(0).subscribe(() => {
        this.fullmode = true;
        this.cd.detectChanges();
      });
    }
    this.isVisibleChange.emit(this.isVisible);
  }
  ngOnInit() {
    this.settingService
      .OnSettingChange(SettingType.Voice)
      .subscribe((option) => {
        this.voiceOptions = option;
      });
    this.settingService
      .OnSettingChange(SettingType.AutoZoomAudio)
      .subscribe((option) => {
        this.autoZoomAudio = option.value;
      });

    this.ngZone.runOutsideAngular(() => {
      interval(1000).subscribe(() => {
        if (this.isVisible) {
          this.cd.detectChanges();
        }
      });
    });
  }

  ngAfterViewInit() {
    this.settingService
      .OnSettingChange(SettingType.Volume)
      .subscribe((option) => {
        this.volume = option.value / 100;
        this.audioPlayer.nativeElement.volume = option.value / 100;
      });
    this.settingService
      .OnSettingChange(SettingType.AudioSpeed)
      .subscribe((option) => {
        this.audioSpeed = option.value;
        this.audioPlayer.nativeElement.playbackRate = option.value;
      });
  }

  async OnAudioPlay() {
    if (this.isStreaming) {
      if (this.audioPlayer.nativeElement.paused) {
        this.audioPlayer.nativeElement.play();
        return;
      }

      this.audioPlayer.nativeElement.pause();
      return;
    }
    this.estimatedDuration = 0;

    try {
      if (!this.accountService.isAuthenticated()) {
        this.toastservice.show(
          ToastType.Error,
          'Bạn phải <b>đăng nhập</b> để nghe audio từ chương này',
        );
        return;
      }
      this.audioPlayer.nativeElement.pause(); // Stop current audio
      this.audioPlayer.nativeElement.currentTime = 0; // Reset to start
      let audioStream = null;
      this.isStreaming = true;
      try {
        audioStream = await this.novelService.getAudioOfChapter(
          this.chapter!.id,
          this.voiceOptions?.value,
        );
      } catch (error) {
        this.toastservice.show(
          ToastType.Error,
          'Có lỗi xảy ra trong quá trình phát audio, vui lòng thử lại sau!',
        );
        this.isStreaming = false;
        return;
      }
      const reader = audioStream.getReader();
      const mediaSource = new MediaSource();

      this.audioPlayer.nativeElement.src = URL.createObjectURL(mediaSource);
      this.audioPlayer.nativeElement.volume = this.volume;
      this.audioPlayer.nativeElement.playbackRate = this.audioSpeed;
      mediaSource.onsourceopen = async () => {
        const sourceBuffer = mediaSource.addSourceBuffer('audio/mpeg');
        while (this.isStreaming) {
          const { done, value } = await reader.read();
          if (done) {
            mediaSource.endOfStream();
            console.log('duration: ', mediaSource.duration);
            this.estimatedDuration = mediaSource.duration;
            break;
          }
          sourceBuffer.appendBuffer(value);
          // Đợi sourceBuffer rảnh trước khi append
          if (sourceBuffer.updating) {
            await new Promise((resolve) =>
              sourceBuffer.addEventListener('updateend', resolve, {
                once: true,
              }),
            );
          }
          const chunkDuration = value.length / BYTES_PER_SECOND;
          this.estimatedDuration += chunkDuration;
        }
      };
      mediaSource.onsourceended = this.onEndAudio;

      this.audioPlayer.nativeElement.play(); // Phát ngay khi có dữ liệu

      console.log('TTS Audio streaming started');
    } catch (error) {
      this.toastservice.show(
        ToastType.Error,
        'Có lỗi xảy ra trong quá trình phát audio, vui lòng thử lại sau!',
      );
      // this.audioPlayer.nativeElement.pause();
      // this.isStreaming = false;
    }
  }

  onEndAudio() {
    this.onEndAudioEvent.emit();
  }

  ngOnChanges(changes: SimpleChanges) {
    console.log(changes);
  }

  seekAudio(event: MouseEvent, progressBar: any) {
    if (this.audioPlayer.nativeElement.paused) return;
    this.audioPlayer.nativeElement.pause();
    // const bar: HTMLElement = progressBar.nativeElement;
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percent = clickX / rect.width;
    const newTime = percent * this.estimatedDuration;

    this.audioPlayer.nativeElement.currentTime = newTime;
    this.audioPlayer.nativeElement.play();
  }
  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${this.padZero(secs)}`;
  }

  padZero(num: number): string {
    return num < 10 ? '0' + num : num.toString();
  }

  onChoiceVoice(event: any) {
    this.isStreaming = false;
    this.settingService.Set(SettingType.Voice, event);
  }

  moreAudioOption() {
    let setting =
      this.dynamicLoadingService.createDynamicComponent(AppSettingComponent);
    setting?.instance?.open(3);
  }
  rushAudio(time: number) {
    const currentTime = this.audioPlayer.nativeElement.currentTime;
    this.audioPlayer.nativeElement.currentTime = Math.max(
      Math.min(currentTime + time, this.estimatedDuration),
      0,
    );
  }

  clickOutside() {
    // console.log('click outside',this.autoZoomAudio);
    // if (this.autoZoomAudio) {
    this.fullmode = false;
    // }
  }
}
