import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  inject,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { NovelService } from '@services/novel.service';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { Observable, catchError, of, map } from 'rxjs';

@Component({
  selector: 'main[app-author]',
  templateUrl: './author.component.html',
  styleUrls: ['./author.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class AuthorComponent implements OnInit {
  private seoService = inject(SeoService);
  private novelService = inject(NovelService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private cd = inject(ChangeDetectorRef);

  authorName: string = '';
  novels: Novel[] = [];
  isLoading = true;
  currentPage = 1;
  totalPages = 0;
  totalNovels = 0;

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const authorSlug = params.get('authorSlug');
      if (authorSlug) {
        // Decode author name from slug
        this.authorName = this.decodeAuthorName(authorSlug);
        this.loadAuthorNovels();
        this.setAuthorSEO();
      } else {
        this.router.navigate(['/not-found']);
      }
    });
  }

  private decodeAuthorName(slug: string): string {
    // Convert slug back to author name
    return slug
      .replace(/-/g, ' ')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  private loadAuthorNovels(): void {
    this.isLoading = true;

    this.novelService
      .getNovels()
      .pipe()
      .subscribe((res) => {
        if (res.data) {
          this.novels = res.data.novels;
          this.totalPages = res.data.totalpage;
          this.currentPage = res.data.page;
        }
        this.isLoading = false;
        this.cd.markForCheck();
      });
  }

  private setAuthorSEO(): void {
    const seoData = {
      title: `Truyện của ${this.authorName} - Đọc Online Miễn Phí`,
      description: `Khám phá tất cả tác phẩm của tác giả ${this.authorName} tại SayTruyenHot. Đọc truyện chữ online miễn phí, cập nhật nhanh nhất với giao diện đẹp và trải nghiệm tuyệt vời.`,
      url: `https://saytruyenhot.com/tac-gia/${this.authorName.toLowerCase().replace(/\s+/g, '-')}`,
      image: 'https://static.saytruyenhot.com/public/logo.png',
      type: 'website' as const,
    };

    this.seoService.setSEOData(seoData);

    // Breadcrumb structured data
    const breadcrumbSchema = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Trang chủ',
          item: 'https://saytruyenhot.com',
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Tác giả',
          item: 'https://saytruyenhot.com/tac-gia',
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: this.authorName,
          item: `https://saytruyenhot.com/tac-gia/${this.authorName.toLowerCase().replace(/\s+/g, '-')}`,
        },
      ],
    };

    this.seoService.addStructuredData([breadcrumbSchema]);
  }

  trackByNovel(index: number, novel: Novel): number {
    return novel.id;
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAuthorNovels();
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
