<ng-container *ngIf="novel">
  <div
    class="transition-colors border border-transparent hover:border-primary-100 p-1 sm:p-1.5 bg-white dark:bg-dark-700 rounded-lg shadow-[0px_1px_6px_1px_rgba(0,0,0,0.1)] h-full overflow-hidden"
  >
    <div class="container-card-v1" style="padding-bottom: 130%">
      <a
        [routerLink]="['/truyen', novel.url + '-' + novel.id]"
        routerLinkActive="active"
        class="novel-link"
        [title]="'Đọc truyện ' + novel.title + ' online'"
        [attr.aria-label]="'Đọc truyện ' + novel.title"
      >
        <img
          loading="lazy"
          [src]="novel.coverImage"
          [alt]="'Bìa truyện ' + novel.title"
          class="novel-image"
          width="160"
          height="200"
          onerror="this.src='https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'"
        />
      </a>

      <button
        (click)="onButtonClick(novel)"
        title="Đọc ngay truyện {{ novel.title }}"
        [attr.aria-label]="'Đọc ngay truyện ' + novel.title"
        class="novel-button"
      >
        <ng-container
          *ngTemplateOutlet="iconRightRef || iconRead"
        ></ng-container>
      </button>
    </div>
    <a
      (mouseenter)="onHoverNovel(true)"
      (mouseleave)="onHoverNovel(false)"
      [routerLink]="['/truyen', novel.url + '-' + novel.id]"
      class="dark:bg-neutral-800"
      [title]="'Đọc truyện ' + novel.title"
      [attr.aria-label]="'Xem chi tiết truyện ' + novel.title"
    >
      <h3 class="hover:text-primary-100 text-sm mt-2 font-medium line-clamp-1">
        {{ novel.title }}
      </h3>
    </a>
    <span class="card-footer-v1">
      <svg
        [class.hidden]="novel.status !== 1"
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
        class="size-4 text-lime-500"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <path
            fill="currentColor"
            d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"
          ></path>
        </g>
      </svg>
      <p class="chapter-title">{{ novel.numChapter }} Chương</p>
    </span>
  </div>
</ng-container>

<ng-content />

<ng-template #iconRead>
  <svg viewBox="0 0 48 48" class="size-4" fill="currentColor">
    <title>Đọc ngay</title>
    <g id="Layer_2" data-name="Layer 2">
      <g id="invisible_box" data-name="invisible box">
        <rect width="48" height="48" fill="none" />
      </g>
      <g id="icons_Q2" data-name="icons Q2">
        <path
          d="M44.9,23.2l-38-18L6,5A2,2,0,0,0,4,7L9.3,23H24a2.1,2.1,0,0,1,2,2,2,2,0,0,1-2,2H9.3L4,43a2,2,0,0,0,2,2l.9-.2,38-18A2,2,0,0,0,44.9,23.2Z"
        />
      </g>
    </g>
  </svg>
</ng-template>
