import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'defaultDescription',
  standalone: false,
})
export class NovelDescriptionPipe implements PipeTransform {
  constructor() {}
  fillDescription(
    description: string | null | undefined,
    params?: { url: any; id: any; title: any },
    link = true,
    replaceReturn = false,
  ): any {
    if ((!description || description === 'None') && params) {
      let url = `/truyen-tranh/${params.url}-${params.id}`;
      let content = !link ? params.title : `<b> ${params.title} </b>`;
      return `Đọc truyện ${content} bản full đầy đủ chap mới nhất tại - SayTruyenHot.
      Bạn đọc đừng quên để lại bình luận và chia sẻ, ủng hộ Mê truyện mới ra các chương mới nhất của truyện ${content}.`;
    }
    if (replaceReturn) {
      return description?.replaceAll('\n', '<br>');
    }
    return description;
  }
  transform(
    value?: any,
    id?: number,
    title?: string,
    url?: string,
    replaceReturn?: boolean,
  ): string {
    return this.fillDescription(
      value,
      { url: url, id: id, title: title },
      true,
      replaceReturn,
    );
  }
}
