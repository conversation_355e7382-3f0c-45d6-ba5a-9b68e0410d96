export enum NovelStatus {
  ALL = -1,
  ONGOING = 0,
  COMPLETED = 1,
}
export enum TranslationType {
  ALL = -1,
  Machine = 0,
  Human = 1,
}
export enum WordCountRange {
  ALL = -1,
  Under100k = 0,
  Between100KAnd500K = 1,
  Over500k = 2,
}

export enum SortType {
  Chapter,
  LastUpdate,
  TopFollow,
  TopComment,
  NewNovel,
  TopDay,
  TopWeek,
  TopMonth,
  TopAll,
}

export enum TopType {
  Day = 1,
  Week = 2,
  Month = 3,
}
export enum Level {
  LuyenKhiKy = 200,
  TruCoKy = 1000,
  KetDanKy = 5000,
  NguyenAnhKy = 10000,
  HoaThanKy = 60000,
  LuyenHuky = 120000,
  HopTheky = 600000,
  DaiThuaKy = 1000000,
  ChanTien = 1200000,
  KiemTien = 1800000,
  ThaiAtKiemTien = 2400000,
  DaiLa = 3000000,
  DaoTo = 3500000,
}

export enum UserExpType {
  Chapter = 10,
  Advertisement = 20,
  Comment = 30,
}

export enum FeatureFlags {
  FEATURE_COIN = 'FEATURE_COIN',
  FEATURE_VIP = 'FEATURE_VIP',
  FEATURE_DOWNLOAD_NOVEL = 'FEATURE_DOWNLOAD_NOVEL',
}
