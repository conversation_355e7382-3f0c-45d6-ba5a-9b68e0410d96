import { Component, Input, NgZone } from '@angular/core';
import { IServiceResponse } from '@schemas/ResponseType';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { timer } from 'rxjs';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  selector: '[app-user-info-popup]',
  templateUrl: './user-info-popup.component.html',
  styleUrl: './user-info-popup.component.scss',
  standalone: false,
})
export class UserInfoPopupComponent implements IPopupComponent {
  isLoadInfoUser = false;
  @Input() UserInfo?: User;
  isShowInfoUser = false;
  constructor(
    private accountService: AccountService,
    private ngZone: NgZone,
  ) {}

  public showUserInfo(userID: number) {
    this.isShowInfoUser = !this.isShowInfoUser;
    this.accountService
      .GetUserById(userID)
      .subscribe((res: IServiceResponse<User>) => {
        this.UserInfo = res.data;
        // this.UserInfo!.levelInfo = this.levelService.getLevelUser(
        //   this.UserInfo!.experience!,
        //   this.UserInfo!.typeLevel!,
        // );

        this.isShowInfoUser = true;
        this.isLoadInfoUser = false;
        timer(500).subscribe(() => {
          this.isLoadInfoUser = true;
        });
      });
  }
  show(o: any) {
    const { userID } = o;
    this.showUserInfo(userID);
    return new Promise((resolve) => {});
  }
}
