<ng-container *ngIf="novel">
    <a
      [routerLink]="['/truyen', novel.url + '-' + novel.id]"
      class="cardv2-link-image"
      [title]="'Đ<PERSON><PERSON> truyện ' + novel.title + ' online'"
      [attr.aria-label]="'Xem chi tiết truyện ' + novel.title"
    >
      <img
        loading="lazy"
        class="cardv2-image"
        [src]="novel.coverImage"
        [alt]="'Bìa truyện ' + novel.title + ' - ' + novel.author"
        width="120"
        height="160"
        onerror="this.src='https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'"
      />
    </a>
    <div class="flex flex-col pl-2 pr-4 py-1 w-full">
      <div class="w-full flex justify-between flex-col">
        <a
          [routerLink]="['/truyen', novel.url + '-' + novel.id]"
          class="mt-1"
          [title]="'Đ<PERSON><PERSON> truyện ' + novel.title + ' - ' + novel.author"
          [attr.aria-label]="'Xem chi tiết truyện ' + novel.title"
        >
          <h3 class="cardv2-title">
            {{ novel.title }}
            <span class="text-xs font-normal text-gray-500"
              >({{ novel.numChapter }} chương)</span
            >
          </h3>
        </a>
        <div class="flex-between">
          <a
            [routerLink]="['/tac-gia', novel.author]"
            class="flex-start gap-2"
            [title]="'Tác giả: ' + novel.author"
            [attr.aria-label]="'Xem thông tin tác giả ' + novel.author"
          >
            <div app-icon-author class="size-3"></div>
            <span class="cardv2-author">{{ novel.author }}</span>
          </a>

          <ng-container
            *ngFor="let tag of novel.genres | slice: 0 : 1; index as i"
          >
            <a
              [routerLink]="['/the-loai', tag.slug]"
              class="tag bg-accent text-pretty"
              [title]="'Xem truyện thể loại ' + tag.title"
              [attr.aria-label]="'Tìm kiếm truyện thể loại ' + tag.title"
            >
              <span class="genre-tag !text-nowrap">
                {{ tag.title }}
              </span>
            </a>
          </ng-container>
        </div>
        <!-- <p class="text-xs mt-1 text-gray-400 flex-shrink-0">
          {{ novel.updateAt | dateAgo }}
        </p> -->
      </div>

      <span
        class="cardv2-description"
        [innerHTML]="
          novel.description
            | defaultDescription: 2 : novel.title : '3'
            | safeHtml
        "
      >
      </span>
    </div>
</ng-container>
