import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  Input,
  NgZone,
  PLATFORM_ID,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { RouterModule } from '@angular/router';
// import { AuthorIconComponent } from '@components/icons/author/author.component';
import { Novel } from '@schemas/Novel';
import { interval, Subscription } from 'rxjs';
import { PinesModule } from 'src/app/shared/pines/pines.module';

@Component({
  selector: '[app-topday]',
  standalone: true,
  imports: [CommonModule, PinesModule, RouterModule],
  templateUrl: './topday.component.html',
  styleUrl: './topday.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TopdayComponent {
  start = 0;
  scrollLeft = 0;
  @ViewChild('carousel') carousel: ElementRef<HTMLElement> | null = null;
  curIndex = 0;
  offset = 0;
  defaultAngle: number = 40;
  @Input() novels: Novel[] = [];
  items: any[] = [];
  subscription?: Subscription;
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private el: ElementRef,
    private cd: ChangeDetectorRef,
    private ngZone: NgZone,
  ) {}
  get computeIndex() {
    return this.curIndex - 3;
  }

  get getWidth() {
    return this.carousel!.nativeElement.clientWidth / 3;
  }
  get halfLength() {
    return Math.round(this.novels.length / 2);
  }
  get Length() {
    return this.novels.length;
  }
  ngOnInit() {
    this.items = this.novels.map((novel, i) => {
      let realIdx = ((i - this.computeIndex + this.Length) % this.Length) - 2;

      return {
        image: novel.coverImage,
        left: `${(realIdx * 100) / 3}%`,
        rotatey: `${(1 - realIdx) * this.defaultAngle}deg`,
        hidden: !(realIdx <= 3 && realIdx >= -1),
        pivort: realIdx <= 0 ? 'left' : 'right',
        scale: `${this.curIndex == i ? 120 : 100}%`,
        main: i == 1,
      };
    });
  }

  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.ngZone.runOutsideAngular(() => {
        this.subscription = interval(4000).subscribe(() => {
          if (this.isElementInViewport(this.el.nativeElement)) this.Move(1);
        });
      });
    }
  }
  ngOnDestroy() {
    this.clearInterval();
  }

  clearInterval() {
    this.subscription?.unsubscribe();
  }
  isElementInViewport(elem: any) {
    const rect = elem.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
        (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }
  Move(step: number) {
    this.curIndex = (this.curIndex + step + this.Length) % this.Length;

    for (let i = 0; i < this.Length; i++) {
      // this.items[i].rotatey = '0deg'
      let realIdx = ((i - this.computeIndex + this.Length) % this.Length) - 2;
      // this.items[i].rotatey = `${(this.curIndex - i) * this.defaultAngle}deg`;
      this.items[i].rotatey = `${(1 - realIdx) * this.defaultAngle}deg`;

      this.items[i].hidden = !(realIdx <= 3 && realIdx >= -1);
      this.items[i].scale = `${this.curIndex == i ? 120 : 100}%`;
      this.items[i].left = `${(realIdx * 100) / 3}%`;

      if (realIdx < 1) this.items[i].pivort = 'left';
      else if (realIdx > 1) this.items[i].pivort = 'right';
    }
    this.cd.detectChanges();
  }
  // timer: any = null;
  // onScroll(e: Event) {

  //   const width = (e.target as HTMLElement).clientWidth
  //   let scrollLeft = (e.target as HTMLElement).scrollLeft;
  //   if (scrollLeft > width) {
  //     (e.target as HTMLElement).scrollLeft = 0;
  //   }
  //   this.updateOffset(scrollLeft, width);

  //   // this.scrollLeft = scrollLeft;

  // }
}
