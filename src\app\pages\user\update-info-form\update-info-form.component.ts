import { CommonModule, isPlatformServer } from '@angular/common';
import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { ImageService } from '@services/image.service';
import { ToastService, ToastType } from '@services/toast.service';
import { first } from 'rxjs';

@Component({
  selector: '[app-update-info-form]',
  templateUrl: './update-info-form.component.html',
  styleUrl: './update-info-form.component.scss',
  standalone: false,
})
export class UpdateInfoFormComponent {
  user!: User;
  levelUser!: { percent: number; level: string; nextLevel: string };
  avatar =
    'https://static.vecteezy.com/system/resources/previews/002/002/257/non_2x/beautiful-woman-avatar-character-icon-free-vector.jpg';
  avatarTimestamp!: number;
  infoForm: FormGroup = new FormGroup({
    firstName: new FormControl(this.user?.firstName, Validators.required),
    lastName: new FormControl(this.user?.lastName, Validators.required),
    email: new FormControl(this.user?.email, [
      Validators.required,
      Validators.email,
    ]),
    dob: new FormControl(this.user?.dob, Validators.required),
  });
  passwordForm: FormGroup = new FormGroup({
    oldPassword: new FormControl(this.user?.firstName, Validators.required),
    newPassword: new FormControl(this.user?.lastName, Validators.required),
    rePassword: new FormControl(this.user?.lastName, Validators.required),
  });

  submitFailed = false;
  submitInfoFailed = false;
  maxim: string | null = '';
  showPassword = false;
  showNewPassword = false;
  showRePassword = false;
  constructor(
    private accountService: AccountService,
    private toast: ToastService,
    private elementRef: ElementRef,
    private imageService: ImageService,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {}

  @HostListener('document:click', ['$event'])
  onGlobalClick(event: Event): void {
    if (this.infoForm.dirty == false) return;
    const formsElement = this.elementRef.nativeElement.querySelectorAll('form');
    let formInfoElement;
    formInfoElement = formsElement[0];
    if (!formInfoElement.contains(event.target)) {
      this.infoForm.reset(this.user);
    }
  }
  transform(
    dateString: string | undefined,
    FORMAT_DATE = 'DD/MM/YYYY',
  ): string {
    const date = new Date(dateString!);

    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  ngOnInit() {
    if (isPlatformServer(this.platformId)) return;

    this.accountService.GetUserInfo().subscribe((res: any) => {
      if (res.status) {
        this.user = res.data;
        this.avatar = this.user?.avatar ? this.user?.avatar : this.avatar;
        this.maxim = this.user.maxim!;

        this.user.dob = this.transform(this.user.dob, 'YYYY-MM-DD');

        this.updateInfoForm();
      } else {
        console.error(res.message);
      }
    });
  }
  updateInfoForm() {
    if (!this.user) return;
    this.infoForm.setValue({
      firstName: this.user.firstName,
      lastName: this.user.lastName,
      email: this.user.email,
      dob: this.user.dob,
    });
  }
  updateTypelevel(type: number) {
    this.accountService.UpdateTypeLevel(type).subscribe((res: any) => {
      if (res.status == 200) {
        this.user.typeLevel = type;
        this.toast.show(ToastType.Success, res.message);
      } else this.toast.show(ToastType.Error, res.message);
    });
  }
  onFileChange(event: any) {
    if (event.target.files && event.target.files.length) {
      const reader = new FileReader();
      if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];

        const avatar: FormData = new FormData();
        avatar.append('image', file, file.name);
        this.accountService
          .UpdateAvatar(avatar)
          .pipe(first())
          .subscribe((res: any) => {
            if (res.status == 200) {
              this.avatar = res.data;

              this.user.token = this.accountService.getAuthorizationToken();
              this.user.avatar = this.avatar;
              this.accountService.SaveUser(this.user);

              this.imageService.updateImageUrl(this.avatar);
              this.toast.show(ToastType.Success, res.message);
            } else {
              this.toast.show(ToastType.Error, res.message);
            }
          });
      }
    }
  }
  markFormAsUpdated(formGroup: FormGroup) {
    formGroup.markAsPristine();
    formGroup.markAsUntouched();
    formGroup.updateValueAndValidity();
  }
  onDateChange(event: any) {
    const input = event.target.value; // Format: yyyy-MM-dd
    const date = new Date(input);
    if (!isNaN(date.getTime())) {
      this.user!.dob = date.toISOString(); // Update the user Dob in ISO format
    } else {
      console.error('Invalid date');
    }
  }
  onUpdateInfo() {
    if (this.infoForm.valid == false || this.infoForm.dirty == false) {
      this.submitInfoFailed = true;
      this.markFormAsUpdated(this.infoForm);
      return;
    }
    this.submitInfoFailed = false;
    const userinfortoUpdate = this.infoForm.value;
    for (const key in userinfortoUpdate) {
      if (
        userinfortoUpdate.hasOwnProperty(key) &&
        typeof userinfortoUpdate[key] === 'string'
      ) {
        userinfortoUpdate[key] = userinfortoUpdate[key]
          .trim()
          .replace(/\s+/g, ' ');
      }
    }

    userinfortoUpdate.dob = new Date(userinfortoUpdate.dob).toISOString();

    this.accountService
      .UpdateInfo(userinfortoUpdate)
      .pipe(first())
      .subscribe((res: any) => {
        if (res.status == 200) {
          //Cập nhật lại user
          this.user = res.data;

          this.user.dob = this.transform(this.user.dob, 'YYYY-MM-DD');

          this.markFormAsUpdated(this.infoForm);
          this.toast.show(ToastType.Success, res.message);
        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      });
  }
  onUpdatePassword() {
    if (this.passwordForm.invalid) {
      this.submitFailed = true;
      this.markFormAsUpdated(this.passwordForm);
    } else {
      this.submitFailed = false;
      const updatepassword = this.passwordForm.value;
      this.accountService
        .UpdatePassword(updatepassword)
        .pipe(first())
        .subscribe((res: any) => {
          if (res.status == 200) {
            this.markFormAsUpdated(this.passwordForm);
            this.toast.show(ToastType.Success, res.message);
          } else {
            this.toast.show(ToastType.Error, res.message);
          }
        });
    }
  }
  onUpdateMaxim(m: string | null) {
    if (m == this.user.maxim) return;

    this.accountService
      .UpdateMaxim(m)
      .pipe(first())
      .subscribe((res: any) => {
        if (res.status == 200) {
          this.user.maxim = m!;
          this.toast.show(ToastType.Success, res.message);
        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      });
  }
  onSettingClick() {
    this.toast.show(ToastType.Info, 'Tính năng đang phát triển');
  }
  isControlInvalid(control: string, form: FormGroup): boolean {
    const ctrl = form.get(control);
    return ctrl ? ctrl.invalid && (ctrl.dirty || ctrl.touched) : false;
  }
}
