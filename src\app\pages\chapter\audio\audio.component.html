<div
  (appClickOutside)="clickOutside()"
  #audioTTS
  class="fixed p-2 z-50 bottom-0 left-1/2 -translate-x-1/2 w-full xs:w-fit text-black bg-transparent flex justify-center"
>
  <div [@openDialog] *ngIf="isVisible">
    <audio #audioPlayer style="width: 100%; opacity: 1"></audio>

    <div class="flex flex-col items-center group/he select-none">
      <div class="relative z-0 h-20 -mb-2 transition-all duration-200">
        <img
          (click)="fullmode = !fullmode"
          [src]="chapter?.novel?.coverImage"
          [alt]="chapter?.novel?.title"
          [ngStyle]="{
            'animation-play-state': audioPlayer.paused ? 'paused' : 'running',
          }"
          loading="lazy"
          class="cursor-pointer animate-[spin_3s_linear_infinite] w-32 h-32 duration-500 object-cover border-4 rounded-full shadow-md border-zinc-400 border-spacing-5 transition-all"
        />
        <div
          class="absolute z-10 w-8 h-8 bg-white border-4 rounded-full shadow-sm border-zinc-400 top-12 left-12"
        ></div>
        <button
          class="absolute z-20 top-14 left-14"
          (click)="setVisible(false)"
        >
          <svg
            class="size-4"
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 0 512.001 512.001"
            xml:space="preserve"
            fill="#000000"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <path
                style="fill: #ff6465"
                d="M256.001,512c141.384,0,255.999-114.615,255.999-256.001C512.001,114.615,397.386,0,256.001,0 S0.001,114.615,0.001,256.001S114.616,512,256.001,512z"
              ></path>
              <path
                style="opacity: 0.1; enable-background: new"
                d="M68.873,256.001c0-129.706,96.466-236.866,221.564-253.688 C279.172,0.798,267.681,0,256.001,0C114.616,0,0.001,114.615,0.001,256.001S114.616,512.001,256,512.001 c11.68,0,23.171-0.798,34.436-2.313C165.339,492.865,68.873,385.705,68.873,256.001z"
              ></path>
              <path
                style="fill: #ffffff"
                d="M313.391,256.001l67.398-67.398c4.899-4.899,4.899-12.842,0-17.74l-39.65-39.65 c-4.899-4.899-12.842-4.899-17.74,0l-67.398,67.398l-67.398-67.398c-4.899-4.899-12.842-4.899-17.74,0l-39.65,39.65 c-4.899,4.899-4.899,12.842,0,17.74l67.398,67.398l-67.398,67.398c-4.899,4.899-4.899,12.842,0,17.741l39.65,39.65 c4.899,4.899,12.842,4.899,17.74,0l67.398-67.398L323.4,380.79c4.899,4.899,12.842,4.899,17.74,0l39.65-39.65 c4.899-4.899,4.899-12.842,0-17.741L313.391,256.001z"
              ></path>
            </g>
          </svg>
        </button>
      </div>

      <div
        (click)="fullmode = true"
        class="relative p-4 z-30 flex flex-col transition-all duration-300 bg-white shadow-md rounded-2xl shadow-zinc-400"
        [ngClass]="{
          'h-20 w-[12.5rem]': !fullmode,
          'h-32 w-full xs:w-80': fullmode,
        }"
      >
        <div [ngClass]="{ hidden: !fullmode }" class="w-full justify-center">
          <div
            class="flex flex-col justify-center items-center text-xs w-full mb-1 overflow-hidden"
          >
            <a
              [routerLink]="[
                '/truyen',
                chapter?.novel?.url + '-' + chapter?.novel?.id,
              ]"
              class="text-base font-bold line-clamp-1 hover:underline"
            >
              {{ chapter?.novel?.title }}
            </a>
            <a
              class="hover:underline"
              [routerLink]="[
                '/',
                chapter?.novel?.url,
                'chuong-' + chapter?.slug,
                chapter?.id,
              ]"
            >
              {{ chapter?.title }}
            </a>
          </div>
        </div>
        <div
          class="text-xs px-1 gap-2 font-medium flex flex-row w-full items-center justify-between"
        >
          <span
            [ngClass]="{ hidden: !fullmode }"
            class="text-slate-500 transition-all duration-500 dark:text-slate-400"
          >
            {{ formatTime(audioPlayer.currentTime) }}
          </span>
          <div class="relative w-full">
            <div
              class="cursor-pointer bg-gray-200 transition-all duration-500 dark:bg-gray-700 rounded-full overflow-hidden"
              #progressBar
              (click)="seekAudio($event, progressBar)"
            >
              <div
                class="bg-cyan-500 transition-all duration-500 dark:bg-cyan-400 w-0 h-1.5"
                [style.width.%]="
                  (audioPlayer.currentTime * 100) / estimatedDuration
                "
                role="progressbar"
                aria-label="music progress"
                [attr.aria-valuenow]="audioPlayer.currentTime"
                aria-valuemin="0"
                [attr.aria-valuemax]="estimatedDuration"
              ></div>
            </div>
            <div
              class="ring-cyan-500 transition-all duration-500 dark:ring-cyan-400 ring-2 absolute -top-1/2 size-3 flex-center bg-white rounded-full shadow"
              [style.left.%]="
                (audioPlayer.currentTime * 100) / estimatedDuration
              "
            >
              <div
                class="w-1.5 h-1.5 bg-cyan-500 transition-all duration-500 dark:bg-cyan-400 rounded-full ring-1 ring-inset ring-slate-900/5"
              ></div>
            </div>
          </div>
          <span
            [ngClass]="{ hidden: !fullmode }"
            class="text-slate-500 transition-all duration-500 dark:text-slate-400"
          >
            {{ formatTime(estimatedDuration) }}
          </span>
        </div>
        <div
          class="mt-1 flex flex-row items-center justify-between flex-grow gap-5"
        >
          <div
            app-select
            [ngClass]="{ hidden: !fullmode }"
            *ngIf="voiceOptions"
            (selectedValueChange)="onChoiceVoice($event)"
            [isFit]="false"
            class="ml-1 inline-flex items-center"
            direct="up"
            [options]="voiceOptions.options!"
            [selectedValue]="voiceOptions.value"
            size="small"
          >
            <ng-template #customBtn>
              <svg
                class="size-5 peer-checked/playMode:hidden"
                viewBox="0 0 512 512"
                xmlns="http://www.w3.org/2000/svg"
                fill="#000000"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g
                  id="SVGRepo_tracerCarrier"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></g>
                <g id="SVGRepo_iconCarrier">
                  <path
                    fill="var(--ci-primary-color, #000000)"
                    d="M342.322,104.837A100.443,100.443,0,0,1,342.457,231L367.4,251.02a132.373,132.373,0,0,0-.178-166.261Z"
                    class="ci-primary"
                  ></path>
                  <path
                    fill="var(--ci-primary-color, #000000)"
                    d="M432.147,32.4,407.158,52.55a183.6,183.6,0,0,1,.248,230.594l25.03,20.1A216.053,216.053,0,0,0,432.147,32.4Z"
                    class="ci-primary"
                  ></path>
                  <path
                    fill="var(--ci-primary-color, #000000)"
                    d="M347.6,343.656l-72.822-47.334,27.455-50.334A80.23,80.23,0,0,0,312,207.681V128a112,112,0,0,0-224,0v79.681a80.236,80.236,0,0,0,9.768,38.308l27.455,50.333L52.4,343.656A79.725,79.725,0,0,0,16,410.732V496H384V410.732A79.725,79.725,0,0,0,347.6,343.656ZM352,464H48V410.732a47.836,47.836,0,0,1,21.841-40.246l97.661-63.48-41.641-76.34A48.146,48.146,0,0,1,120,207.681V128a80,80,0,0,1,160,0v79.681a48.139,48.139,0,0,1-5.861,22.984L232.5,307.007l97.662,63.479A47.838,47.838,0,0,1,352,410.732Z"
                    class="ci-primary"
                  ></path>
                </g>
              </svg>
            </ng-template>
          </div>
          <div
            [ngClass]="{ 'mt-2': !fullmode }"
            class="flex flex-row items-center justify-center flex-grow gap-4"
          >
            <div
              class="flex-center w-12 h-full focus:bg-black cursor-pointer"
              (click)="rushAudio(-10)"
            >
              <svg
                class="size-7"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g
                  id="SVGRepo_tracerCarrier"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></g>
                <g id="SVGRepo_iconCarrier">
                  <path
                    d="M9.5415 16.6708C9.1315 16.6708 8.7915 16.3308 8.7915 15.9208V12.5308L8.6015 12.7508C8.3215 13.0608 7.8515 13.0808 7.5415 12.8108C7.2315 12.5308 7.2115 12.0608 7.4815 11.7508L8.9815 10.0808C9.1915 9.85081 9.5215 9.77081 9.8115 9.88081C10.1015 9.99081 10.2915 10.2708 10.2915 10.5808V15.9308C10.2915 16.3408 9.9615 16.6708 9.5415 16.6708Z"
                    fill="#292D32"
                  ></path>
                  <path
                    d="M12.0011 3.47974C11.9211 3.47974 11.8411 3.48974 11.7611 3.48974L12.5811 2.46974C12.8411 2.14974 12.7911 1.66974 12.4611 1.41974C12.1411 1.15974 11.6711 1.20974 11.4111 1.53974L9.44109 3.99974C9.43109 4.00974 9.43109 4.01974 9.42109 4.03974C9.39109 4.07974 9.37109 4.12974 9.35109 4.16974C9.33109 4.21974 9.31109 4.25974 9.30109 4.29974C9.29109 4.34974 9.29109 4.38974 9.29109 4.43974C9.29109 4.48974 9.29109 4.53974 9.29109 4.58974C9.29109 4.60974 9.29109 4.61974 9.29109 4.63974C9.30109 4.66974 9.32109 4.68974 9.33109 4.72974C9.35109 4.77974 9.36109 4.81974 9.39109 4.85974C9.42109 4.89974 9.45109 4.93974 9.49109 4.97974C9.51109 4.99974 9.53109 5.02974 9.55109 5.04974C9.56109 5.05974 9.58109 5.05974 9.59109 5.06974C9.62109 5.08974 9.65109 5.10974 9.69109 5.11974C9.74109 5.14974 9.79109 5.16974 9.84109 5.17974C9.88109 5.19974 9.91109 5.19974 9.95109 5.19974C9.98109 5.19974 10.0011 5.20974 10.0311 5.20974C10.0511 5.20974 10.0811 5.19974 10.1011 5.18974C10.1311 5.18974 10.1611 5.18974 10.2011 5.18974C10.8411 5.03974 11.4411 4.96974 12.0111 4.96974C16.5011 4.96974 20.1511 8.61974 20.1511 13.1097C20.1511 17.5997 16.5011 21.2497 12.0111 21.2497C7.52109 21.2497 3.87109 17.5997 3.87109 13.1097C3.87109 11.3697 4.44109 9.68974 5.52109 8.24974C5.77109 7.91974 5.70109 7.44974 5.37109 7.19974C5.04109 6.94974 4.57109 7.01974 4.32109 7.34974C3.04109 9.04974 2.37109 11.0397 2.37109 13.1097C2.37109 18.4197 6.69109 22.7497 12.0111 22.7497C17.3311 22.7497 21.6511 18.4297 21.6511 13.1097C21.6511 7.78974 17.3111 3.47974 12.0011 3.47974Z"
                    fill="#292D32"
                  ></path>
                  <path
                    d="M14 16.6703C12.48 16.6703 11.25 15.4403 11.25 13.9203V12.5703C11.25 11.0503 12.48 9.82031 14 9.82031C15.52 9.82031 16.75 11.0503 16.75 12.5703V13.9203C16.75 15.4403 15.52 16.6703 14 16.6703ZM14 11.3303C13.31 11.3303 12.75 11.8903 12.75 12.5803V13.9303C12.75 14.6203 13.31 15.1803 14 15.1803C14.69 15.1803 15.25 14.6203 15.25 13.9303V12.5803C15.25 11.8903 14.69 11.3303 14 11.3303Z"
                    fill="#292D32"
                  ></path>
                </g>
              </svg>
            </div>
            <label
              for="playStatus"
              class="flex-center size-10 rounded-full cursor-pointer bg-gray-200"
            >
              <input
                type="checkbox"
                name="playStatus"
                id="playStatus"
                (input)="OnAudioPlay()"
                class="hidden peer/playStatus"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="feather feather-play ml-1"
                [ngClass]="{ hidden: !audioPlayer.paused }"
              >
                <polygon points="5 3 19 12 5 21 5 3"></polygon>
              </svg>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="feather feather-pause"
                [ngClass]="{
                  'inline-block': !audioPlayer.paused,
                  hidden: audioPlayer.paused,
                }"
              >
                <rect x="6" y="4" width="4" height="16"></rect>
                <rect x="14" y="4" width="4" height="16"></rect>
              </svg>
            </label>
            <div
              class="flex-center w-12 h-full cursor-pointer"
              (click)="rushAudio(10)"
            >
              <svg
                class="size-7"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g
                  id="SVGRepo_tracerCarrier"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></g>
                <g id="SVGRepo_iconCarrier">
                  <path
                    d="M19.6916 7.34849C19.4416 7.01849 18.9716 6.94849 18.6416 7.19849C18.3116 7.44849 18.2416 7.91849 18.4916 8.24849C19.5716 9.68849 20.1416 11.3685 20.1416 13.1085C20.1416 17.5985 16.4916 21.2485 12.0016 21.2485C7.51156 21.2485 3.86156 17.5985 3.86156 13.1085C3.86156 8.61849 7.51156 4.97849 12.0016 4.97849C12.5816 4.97849 13.1716 5.04849 13.8116 5.19849C13.8416 5.20849 13.8716 5.19849 13.9116 5.19849C13.9316 5.19849 13.9616 5.21849 13.9816 5.21849C14.0116 5.21849 14.0316 5.20849 14.0616 5.20849C14.1016 5.20849 14.1316 5.19849 14.1616 5.18849C14.2116 5.17849 14.2616 5.15849 14.3116 5.12849C14.3416 5.10849 14.3816 5.09849 14.4116 5.07849C14.4216 5.06849 14.4416 5.06849 14.4516 5.05849C14.4816 5.03849 14.4916 5.00849 14.5116 4.98849C14.5516 4.94849 14.5816 4.91849 14.6116 4.86849C14.6416 4.82849 14.6516 4.77849 14.6716 4.73849C14.6816 4.70849 14.7016 4.67849 14.7116 4.64849C14.7116 4.62849 14.7116 4.61849 14.7116 4.59849C14.7216 4.54849 14.7216 4.49849 14.7116 4.44849C14.7116 4.39849 14.7116 4.35849 14.7016 4.30849C14.6916 4.26849 14.6716 4.22849 14.6516 4.17849C14.6316 4.12849 14.6116 4.07849 14.5816 4.03849C14.5716 4.01849 14.5716 4.00849 14.5616 3.99849L12.5816 1.52849C12.3216 1.20849 11.8516 1.15849 11.5316 1.40849C11.2116 1.66849 11.1616 2.13849 11.4116 2.45849L12.2316 3.47849C12.1516 3.47849 12.0716 3.46849 11.9916 3.46849C6.68156 3.46849 2.35156 7.78849 2.35156 13.1085C2.35156 18.4285 6.67156 22.7485 11.9916 22.7485C17.3116 22.7485 21.6316 18.4285 21.6316 13.1085C21.6416 11.0385 20.9616 9.04849 19.6916 7.34849Z"
                    fill="#292D32"
                  ></path>
                  <path
                    d="M9.5415 16.6708C9.1315 16.6708 8.7915 16.3308 8.7915 15.9208V12.5308L8.6015 12.7508C8.3215 13.0608 7.8515 13.0808 7.5415 12.8108C7.2315 12.5308 7.2115 12.0608 7.4815 11.7508L8.9815 10.0808C9.1915 9.85081 9.5215 9.77081 9.8115 9.88081C10.1015 9.99081 10.2915 10.2708 10.2915 10.5808V15.9308C10.2915 16.3408 9.9615 16.6708 9.5415 16.6708Z"
                    fill="#292D32"
                  ></path>
                  <path
                    d="M14 16.6703C12.48 16.6703 11.25 15.4403 11.25 13.9203V12.5703C11.25 11.0503 12.48 9.82031 14 9.82031C15.52 9.82031 16.75 11.0503 16.75 12.5703V13.9203C16.75 15.4403 15.52 16.6703 14 16.6703ZM14 11.3303C13.31 11.3303 12.75 11.8903 12.75 12.5803V13.9303C12.75 14.6203 13.31 15.1803 14 15.1803C14.69 15.1803 15.25 14.6203 15.25 13.9303V12.5803C15.25 11.8903 14.69 11.3303 14 11.3303Z"
                    fill="#292D32"
                  ></path>
                </g>
              </svg>
            </div>
          </div>

          <div
            [ngClass]="{ hidden: !fullmode }"
            class="flex-center h-full cursor-pointer"
          >
            <svg
              (click)="moreAudioOption()"
              fill="#000000"
              class="size-5"
              viewBox="0 0 32 32"
              enable-background="new 0 0 32 32"
              id="Glyph"
              version="1.1"
              xml:space="preserve"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></g>
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M13,16c0,1.654,1.346,3,3,3s3-1.346,3-3s-1.346-3-3-3S13,14.346,13,16z"
                  id="XMLID_294_"
                ></path>
                <path
                  d="M13,26c0,1.654,1.346,3,3,3s3-1.346,3-3s-1.346-3-3-3S13,24.346,13,26z"
                  id="XMLID_295_"
                ></path>
                <path
                  d="M13,6c0,1.654,1.346,3,3,3s3-1.346,3-3s-1.346-3-3-3S13,4.346,13,6z"
                  id="XMLID_297_"
                ></path>
              </g>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
