.footer-container {
  // margin-top: 2rem; /* Adjusted margin-top for spacing */
  // position: relative;
  // border-top: 1px solid;

  @apply border-gray-300 dark:border-gray-700 mt-8 relative border-t ;
}

.dark .footer-container {
  background: linear-gradient(135deg, #171717 0%, #262626 50%, #171717 100%);
}

.footer-inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.footer-main-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .footer-main-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-main-content {
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }
}

// Brand Section
.footer-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@media (min-width: 1024px) {
  .footer-brand {
    align-items: flex-start;
    text-align: left;
  }
}

.footer-logo {
  margin-bottom: 1rem;
}

.footer-logo-image {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  box-shadow: 0 10px 25px rgba(249, 115, 22, 0.3);
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(249, 115, 22, 0.3));
}

.footer-logo-image:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 35px rgba(249, 115, 22, 0.4);
}

.footer-tagline {
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
  line-height: 1.5;
  max-width: 20rem;
}

.dark .footer-tagline {
  color: #d1d5db;
}

// Section Styling
.footer-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 1024px) {
  .footer-section {
    align-items: flex-start;
  }
}

.footer-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #fb923c;
  display: inline-block;
}

.dark .footer-section-title {
  color: #e5e7eb;
}

// Links Section
.footer-links {
  display: flex;
  flex-direction: column;
}

.footer-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.4rem;
  border-radius: 0.5rem;
}

.footer-link:hover {
  color: #ea580c;
  transform: translateX(0.25rem);
  background: rgba(249, 115, 22, 0.1);
}

.dark .footer-link {
  color: #d1d5db;
}

.dark .footer-link:hover {
  color: #fb923c;
  background: rgba(249, 115, 22, 0.15);
}

.footer-link-icon {
  font-size: 1.125rem;
  transition: transform 0.3s ease;
}

.footer-link:hover .footer-link-icon {
  transform: scale(1.1);
}

.footer-link span {
  font-size: 0.875rem;
  font-weight: 500;
}

// Contact Section
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.dark .footer-contact-item {
  color: #d1d5db;
}

.footer-contact-icon {
  font-size: 1.125rem;
}

.footer-contact-item span {
  font-size: 0.875rem;
}

.footer-social {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-social-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 0.5rem;
}

.dark .footer-social-title {
  color: #d1d5db;
}

.footer-social-links {
  display: flex;
  gap: 1rem;
}

.footer-social-link {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.footer-social-link:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.social-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.footer-social-link.facebook {
  background: #dbeafe;
  color: #2563eb;
}

.footer-social-link.facebook:hover {
  background: #2563eb;
  color: white;
}

.dark .footer-social-link.facebook {
  background: #1e3a8a;
  color: #93c5fd;
}

.dark .footer-social-link.facebook:hover {
  background: #2563eb;
  color: white;
}

.footer-social-link.tiktok {
  background: #f3f4f6;
  color: #374151;
}

.footer-social-link.tiktok:hover {
  background: #000000;
  color: white;
}

.dark .footer-social-link.tiktok {
  background: #1f2937;
  color: #9ca3af;
}

.dark .footer-social-link.tiktok:hover {
  background: #000000;
  color: white;
}

.footer-social-link.twitter {
  background: #e0f2fe;
  color: #0284c7;
}

.footer-social-link.twitter:hover {
  background: #0284c7;
  color: white;
}

.dark .footer-social-link.twitter {
  background: #0c4a6e;
  color: #7dd3fc;
}

.dark .footer-social-link.twitter:hover {
  background: #0284c7;
  color: white;
}

// Footer Bottom
.footer-bottom {
  padding-top: 2rem;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #fb923c, transparent);
  margin-bottom: 1.5rem;
}

.footer-bottom-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-copyright {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.dark .footer-copyright {
  color: #d1d5db;
}

.footer-note {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.dark .footer-note {
  color: #9ca3af;
}
