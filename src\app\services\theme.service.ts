import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private isDarkTheme: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false,
  );

  public IsDark = () => this.isDarkTheme.value;

  constructor() {}

  setDarkTheme(isDarkTheme: boolean) {
    this.isDarkTheme.next(isDarkTheme);
  }

  ToggleTheme() {
    this.isDarkTheme.next(!this.isDarkTheme.value);
  }

  getDarkTheme(): Observable<boolean> {
    return this.isDarkTheme;
  }
}
