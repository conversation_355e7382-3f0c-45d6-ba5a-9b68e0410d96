import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { catchError, map, Observable, of } from 'rxjs';

export const novelResolver: ResolveFn<
  Observable<IServiceResponse<Novel> | null>
> = (route) => {
  let id = route.paramMap.get('slug');
  id = id?.split('-').pop() || '';
  if (!Number.isInteger(Number(id))) return of(null);
  return inject(NovelService)
    .getNovelById(id)
    .pipe(
      map((res) => {
        return res;
      }),
      catchError((err) => {
        return of(null);
      }),
    );
};
