import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';

@Directive({
  selector: '[appClickOutside]',
})
export class ClickOutsideDirective {
  @Output() appClickOutside = new EventEmitter<void>();
  @Input() elementIgnore: HTMLElement | null = null;
  @Input() elementIdIgnore: string | null = null;
  @Input() disabled: boolean = false;
  constructor(private el: ElementRef) {}

  // Lắng nghe sự kiện click trong document
  @HostListener('document:click', ['$event.target']) onClick(
    targetElement: HTMLElement,
  ) {
    if (this.disabled) return; // Nếu directive bị vô hiệu hóa, không làm gì cả
    const clickedInside = this.el.nativeElement.contains(targetElement);
    const ignoredElement = this.elementIgnore?.contains(targetElement);

    let ignoredById = false;
    if (this.elementIdIgnore) {
      const ignoreEl = document.getElementById(this.elementIdIgnore);
      if (ignoreEl) {
        ignoredById = ignoreEl.contains(targetElement);
      }
    }

    if (!clickedInside && !ignoredElement && !ignoredById) {
      this.appClickOutside.emit(); // Phát ra sự kiện khi click ra ngoài
    }
  }
}
