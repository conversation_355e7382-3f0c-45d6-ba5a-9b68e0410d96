import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { fadeInOut, openDialog } from '@components/utils/animation';

@Component({
  selector: '[app-dialog]',
  standalone: true,
  templateUrl: './dialog.component.html',
  styleUrl: './dialog.component.scss',
  imports: [CommonModule],
  animations: [openDialog],
})
export class DialogComponent {
  @Input()
  isVisible = true;

  @Output()
  isVisibleChange = new EventEmitter<boolean>();

  open() {
    this.isVisible = true;
    this.isVisibleChange.emit(this.isVisible);
  }

  close() {
    this.isVisible = false;
    this.isVisibleChange.emit(this.isVisible);
  }
}
