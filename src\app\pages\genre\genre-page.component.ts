import { CommonModule, isPlatformServer } from '@angular/common';
import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import {
  Subject,
  takeUntil,
  switchMap,
  map,
  catchError,
  of,
  BehaviorSubject,
  combineLatest,
} from 'rxjs';

import { SEOData, SeoService } from '@services/seo.service';
import { NovelService } from '@services/novel.service';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';
import { NovelList } from '@schemas/NovelList';
import { IServiceResponse } from '@schemas/ResponseType';
import { FAQItem } from '@components/faq/faq.component';
import globalConfig from 'globalConfig';
import { IFilterOptions } from '@components/utils/constants';
import {
  NovelStatus,
  SortType,
  TranslationType,
  WordCountRange,
} from '@schemas/enum';
import { UrlService } from '@services/url.service';

@Component({
  selector: 'main[app-genre-page]',
  templateUrl: './genre-page.component.html',
  styleUrls: ['./genre-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class GenrePageComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  genre: Genre | null = null;

  public selectedValues: IFilterOptions = {
    sort: -1,
    status: -1,
    translation: -1,
    wordcount: -1,
  };
  // Filter options

  // Loading states
  isLoading = true;
  currentPage = 1;
  totalpage = 0;
  length = 0;
  step = 20;
  novels: Novel[] = [];
  // SEO data

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private novelService: NovelService,
    private cd: ChangeDetectorRef,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {}

  ngOnInit() {
    this.isLoading = true;
    const combined$ = combineLatest([
      this.route.paramMap,
      this.route.queryParamMap,
    ]);
    combined$.subscribe(([params, queryParams]) => {
      const page = params.get('page')?.replace('trang-', '') || 1;
      const slug = params.get('slug') || '';
      if (slug == '' || !Number.isInteger(Number(page))) {
        this.router.navigate(['/not-found']);
        return;
      }

      const status = +(queryParams.get('status') ?? NovelStatus.ALL);
      const sort = +(queryParams.get('sort') ?? SortType.LastUpdate);
      const wordcount = +(queryParams.get('wordcount') ?? WordCountRange.ALL);
      const translation = +(
        queryParams.get('translation') ?? TranslationType.ALL
      );
      const keyword = queryParams.get('keyword') || '';

      this.currentPage = Number(page);
      this.selectedValues = {
        ...this.selectedValues,
        status,
        sort,
        wordcount,
        translation,
        keyword,
      };

      this.genre = this.novelService.getGenreBySlug(slug);
      if (!this.genre) {
        this.router.navigate(['/not-found']);
        return;
      }
      this.searchNovels(this.currentPage, sort, status, wordcount, translation);
    });
  }

  searchNovels(
    page: number,
    sort: number,
    status: number,
    wordcount: number,
    translation: number,
  ) {
    this.isLoading = true;
    this.novelService
      .getNovelsByGenre(
        this.genre?.id!,
        page,
        this.step,
        sort,
        status,
        translation,
        wordcount,
      )
      .subscribe((res: IServiceResponse<NovelList>) => {
        if (res.data) this.novels = res.data?.novels;
        this.length = this.novels.length;
        this.totalpage = res.data?.totalpage || 0;
        this.currentPage = res.data?.page || 1;
        this.isLoading = false;

        // Update SEO after data is loaded
        this.setGenreSEO(this.genre!, this.novels, this.currentPage);

        this.cd.markForCheck();
      });
  }

  executeSearch(data: IFilterOptions) {
    this.selectedValues = data;
    let queryParams: { [key: string]: any } = {};
    if (this.selectedValues.sort !== 1) {
      queryParams['sort'] = this.selectedValues.sort;
    }
    if (this.selectedValues.status !== -1) {
      queryParams['status'] = this.selectedValues.status;
    }
    if (this.selectedValues.translation !== -1) {
      queryParams['translation'] = this.selectedValues.translation;
    }
    if (this.selectedValues.wordcount !== -1) {
      queryParams['wordcount'] = this.selectedValues.wordcount;
    }

    this.router.navigate(['/the-loai/' + this.genre?.slug], {
      queryParams: queryParams,
      queryParamsHandling: 'replace',
      fragment: 'listNovel',
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  setGenreSEO(genre: Genre, novels?: Novel[], page: number = 1): void {
    // Enhanced title with competitive keywords
    const title =
      page > 1
        ? `Truyện ${genre.title} Hay Nhất - Trang ${page} | Top ${genre.title} 2025`
        : `Đọc Truyện ${genre.title} Online Miễn Phí | Top ${genre.title} Hay Nhất 2025`;

    // Enhanced description with long-tail keywords
    const novelCount = novels?.length || genre.quantity || 0;
    const statusText = page > 1 ? ` - Trang ${page}` : '';
    const description = `Kho truyện ${genre.title} lớn nhất Việt Nam với ${novelCount}+ truyện chất lượng cao${statusText}. Đọc truyện ${genre.title} online miễn phí, cập nhật nhanh nhất, full hoàn thành, không quảng cáo. ${genre.description || `Khám phá top truyện ${genre.title} đỉnh cao được độc giả yêu thích nhất.`} Hỗ trợ đọc offline, giao diện đẹp, trải nghiệm tuyệt vời!`;

    // Enhanced keywords with competitive terms
    const competitiveKeywords = [
      `truyện ${genre.title} hay nhất`,
      `đọc ${genre.title} online miễn phí`,
      `top ${genre.title} 2025`,
      `${genre.title} full hoàn thành`,
      `kho truyện ${genre.title} lớn nhất`,
      `${genre.title} cập nhật nhanh`,
      `truyện ${genre.title} chất lượng cao`,
      `${genre.title} không quảng cáo`,
      `đọc ${genre.title} offline`,
      `${genre.title} hot nhất`,
      `${genre.title} trending`,
      `website ${genre.title} số 1`,
    ];

    const baseKeywords =
      'đọc truyện chữ online miễn phí, website truyện số 1 Việt Nam, kho truyện lớn nhất, truyện chất lượng cao, cập nhật nhanh nhất';
    const keywords = `${competitiveKeywords.join(', ')}, ${baseKeywords}`;

    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: globalConfig.APP_NAME,
      twitterCard: 'summary',
      category: genre.title,
      keywords,
      url: this.urlService.getUrl(`/the-loai/${genre.slug}`),
      canonical:
        page > 1
          ? undefined
          : this.urlService.getUrl(`/the-loai/${genre.slug}`),
    };

    this.seoService.setSEOData(seoData);

    // Add structured data
    const schemas = [
      this.seoService.generateGenreSchema(genre, novels || []),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: this.urlService.getUrl('/') },
        {
          name: `Thể loại ${genre.title}`,
          url: this.urlService.getUrl(`/the-loai/${genre.slug}`),
        },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  // FAQ data for genre
  getGenreFAQs(): FAQItem[] {
    if (!this.genre) return [];

    return [
      {
        question: `Thể loại ${this.genre.title} là gì?`,
        answer:
          this.genre.description ||
          `${this.genre.title} là một thể loại truyện chữ phổ biến với nhiều tác phẩm hay và hấp dẫn. Bạn có thể tìm thấy nhiều truyện ${this.genre.title} chất lượng cao tại website của chúng tôi.`,
      },
      {
        question: `Có bao nhiêu truyện ${this.genre.title} trên website?`,
        answer: `Hiện tại chúng tôi có ${this.genre.quantity || 'rất nhiều'} truyện thuộc thể loại ${this.genre.title}, được cập nhật liên tục với các tác phẩm mới nhất và chất lượng nhất.`,
      },
      {
        question: `Làm thế nào để đọc truyện ${this.genre.title} miễn phí?`,
        answer: `Bạn có thể đọc tất cả truyện ${this.genre.title} hoàn toàn miễn phí tại website của chúng tôi. Chỉ cần chọn truyện bạn muốn đọc và bắt đầu thưởng thức ngay lập tức.`,
      },
      {
        question: `Truyện ${this.genre.title} nào đang được yêu thích nhất?`,
        answer: `Các truyện ${this.genre.title} phổ biến nhất được sắp xếp theo lượt xem và đánh giá của độc giả. Bạn có thể lọc theo "Xem nhiều nhất" để tìm những tác phẩm được ưa chuộng.`,
      },
      {
        question: `Website có cập nhật truyện ${this.genre.title} thường xuyên không?`,
        answer: `Chúng tôi cập nhật truyện ${this.genre.title} hàng ngày với các chương mới nhất từ các tác giả. Bạn có thể theo dõi để không bỏ lỡ bất kỳ chương nào.`,
      },
    ];
  }

  // Utility methods
  trackByNovel(_index: number, novel: Novel): number {
    return novel.id;
  }

  isServer(): boolean {
    return isPlatformServer(this.platformId);
  }
}
