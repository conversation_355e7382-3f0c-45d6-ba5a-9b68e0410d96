import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Announcement } from '@schemas/Announcement';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { ChapterPage } from '@schemas/ChapterPage';
import {
  NovelStatus,
  SortType,
  TranslationType,
  UserExpType,
  WordCountRange,
} from '@schemas/enum';
import { Genre } from '@schemas/Genre';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { NovelTopView } from '@schemas/NovelTopView';
import { IServiceResponse } from '@schemas/ResponseType';
import globalConfig from 'globalConfig';
import { Observable, of } from 'rxjs';
import { AccountService } from './account.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class NovelService {
  constructor(
    private httpclient: HttpClient,
    private auth: AccountService,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {}
  getAds() {
    let req = this.httpclient.get(`${this.urlService.API_URL}/ads?`, {
      transferCache: true,
      params: { expiration: 300 },
    });

    return req;
  }
  getHotNovels(page = 1, step = 30) {
    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/hotnovels?${new URLSearchParams({
        page: page.toString(),
        step: step.toString(),
      })}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );

    return req;
  }
  getNovels(
    params: {
      page: string;
      step: string;
      genre: string;
      sort: string;
      status: string;
    } = {} as any,
  ): Observable<IServiceResponse<NovelList>> {
    // function getRandomArbitrary(min: number, max: number) {
    //   return Math.floor(Math.random() * (max - min) + min);
    // }
    // params.page = getRandomArbitrary(1, 100).toString();

    let searchParams = new URLSearchParams(params).toString();
    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novels?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      },
    );
    return req;
  }

  getNovelCoAuthor(novelId: number) {
    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novel/${novelId}/co-author?size=3`,
      {
        transferCache: true,
        params: { expiration: 60 },
      },
    );
    return req;
  }
  getRecommendNovels() {
    return this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/recommend`,
      {
        transferCache: true,
        params: { expiration: 600 },
      },
    );
  }
  getNovelById(id: string): Observable<IServiceResponse<Novel>> {
    let req = this.httpclient.get<IServiceResponse<Novel>>(
      `${this.urlService.API_URL}/novel/${id}`,
      {
        transferCache: true,
        params: { expiration: 120 },
      },
    );

    return req;
  }
  getNovelsByIds(ids: number[]): Observable<IServiceResponse<Novel>> {
    return this.httpclient.get<IServiceResponse<Novel>>(
      `${this.urlService.API_URL}/novelsbyids?ids=${ids.join(',')}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
  }

  getChapterPage(id: number) {
    return this.httpclient.get<IServiceResponse<ChapterPage>>(
      `${this.urlService.API_URL}/novel/chapter/${id}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
  }
  getChapterNeighbors(id: number) {
    return this.httpclient.get<IServiceResponse<Chapter[]>>(
      `${this.urlService.API_URL}/novel/chapter/${id}/neighbors`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
  }

  getChapterContent(chapterId: number) {
    return this.httpclient.get(
      `${this.urlService.API_URL}/chapter-content/${chapterId}`,
    );
  }
  getChapters(novelid: number, page: number = 1, step: number = 200) {
    let searchParams = new URLSearchParams({
      page: page.toString(),
      step: step.toString(),
    }).toString();

    return this.httpclient.get<IServiceResponse<ChapterList>>(
      `${this.urlService.API_URL}/novel/${novelid}/chapters?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      },
    );
  }
  getTopNovels() {
    let req = this.httpclient.get<IServiceResponse<NovelTopView>>(
      `${this.urlService.API_URL}/novel/topview?`,
      {
        transferCache: true,
        params: { expiration: 600 },
      },
    );
    return req;
  }
  getSearchNovel(key: string) {
    return this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/search?keyword=${key}`,
    );
  }
  getAdvanceSearchNovel(
    page = 1,
    step = 40,
    sort = SortType.TopAll,
    status = NovelStatus.ALL,
    genres = '',
    nogenres = '',
    translation = TranslationType.ALL,
    wordcount = WordCountRange.ALL,
    keyword = '',
  ) {
    let params = {
      page: page.toString(),
      step: step.toString(),
      sort: sort.toString(),
      status: status.toString(),
      genres: genres,
      nogenres: nogenres,
      translation: translation.toString(),
      wordcount: wordcount.toString(),
      keyword: keyword,
    };

    let searchParams = new URLSearchParams(params).toString();

    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novel/advance?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      },
    );
    return req;
  }

  getNovelsByGenre(
    genreId: number,
    page = 1,
    step = 40,
    sort = SortType.LastUpdate,
    status = NovelStatus.ALL,
    translation = TranslationType.ALL,
    wordcount = WordCountRange.ALL,
  ) {
    let params = {
      page: page.toString(),
      step: step.toString(),
      sort: sort.toString(),
      status: status.toString(),
      translation: translation.toString(),
      wordcount: wordcount.toString(),
    };
    let searchParams = new URLSearchParams(params).toString();

    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novel/genre/${genreId}?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      },
    );
    return req;
  }

  getSimilarNovel(idnovel: number) {
    let req = this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/similar/${idnovel}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
    return req;
  }

  getGenres(): Observable<Genre[]> {
    return of(genres);
  }

  getGenreBySlug(slug: string) {
    const genre = genres.find((g) => g.slug === slug);
    return genre ? genre : null;
  }

  getGenreById(id: number) {
    const genre = genres.find((g) => g.id === id);
    return genre ? genre : null;
  }
  updateViewAndExp(
    novelId: number,
    chapterId: number,
    exp: UserExpType.Chapter,
  ) {
    let req = this.httpclient.get(
      `${this.urlService.API_URL}/novel/view_exp?novelId=${novelId}&exp=${exp}&chapterId=${chapterId}`,
    );
    return req;
  }
  getAnouncement(): Observable<IServiceResponse<Announcement[]>> {
    let req = this.httpclient.get<IServiceResponse<Announcement[]>>(
      `${this.urlService.API_URL}/announcement`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
    return req;
  }
  onUnFollowClick(idNovel: number) {
    return this.httpclient.get(
      `${this.urlService.API_URL}/user/follow/follow/${idNovel}`,
    );
  }

  async getAudioOfChapter(chapter_id: number, voicser: string) {
    if (!this.auth.isAuthenticated()) {
      throw new Error('User is not authenticated');
    }
    // const params = new URLSearchParams({ token: String(token), chapter_id: chapter_id.toString() })
    const params = new URLSearchParams({
      chapter_id: chapter_id.toString(),
      voice: voicser.toString(),
    });
    const url = `${globalConfig.BASE_CHAT_URL}/tts?${params}`;
    let headers = {
      Authorization: `Bearer ${this.auth.getAuthorizationToken()}`,
    };

    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    });
    if (!response.body) {
      throw new Error('No response body from TTS service');
    }
    return response.body; // Return the response body (stream) to the component
  }
}

const genres = [
  {
    id: 2,
    title: 'bách hợp',
    slug: 'bach-hop',
    description:
      'Truyện xoay quanh tình yêu giữa hai nhân vật nữ, thể hiện tình cảm sâu sắc và cảm động.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1003,
    group: 1,
  },
  {
    id: 4,
    title: 'cung đấu',
    slug: 'cung-dau',
    description:
      'Truyện về các âm mưu, tranh đấu trong cung đình, xoay quanh hậu cung, hoàng hậu, phi tần.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 971,
    group: 3,
  },
  {
    id: 6,
    title: 'cổ đại',
    slug: 'co-dai',
    description:
      'Truyện lấy bối cảnh thời cổ đại, có thể dựa trên lịch sử hoặc là thế giới giả tưởng.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 7128,
    group: 3,
  },
  {
    id: 9,
    title: 'dị giới',
    slug: 'di-gioi',
    description:
      'Truyện về nhân vật chính đến một thế giới khác, có thể là thần thoại, ma pháp hoặc tiên hiệp.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1103,
    group: 2,
  },
  {
    id: 10,
    title: 'dị năng',
    slug: 'di-nang',
    description:
      'Truyện có nhân vật sở hữu năng lực siêu nhiên như điều khiển lửa, nước, đọc suy nghĩ.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 821,
    group: 2,
  },
  {
    id: 13,
    title: 'gia đấu',
    slug: 'gia-dau',
    description:
      'Truyện về đấu đá trong gia tộc, âm mưu giữa các thành viên để tranh giành quyền lợi.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 457,
    group: 3,
  },
  {
    id: 15,
    title: 'huyền huyễn',
    slug: 'huyen-huyen',
    description:
      'Truyện có yếu tố huyền bí, tu luyện, thần tiên, yêu ma hoặc pháp thuật kỳ ảo.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 3819,
    group: 2,
  },
  {
    id: 17,
    title: 'hài hước',
    slug: 'hai-huoc',
    description:
      'Truyện có nội dung vui nhộn, gây cười với những tình huống hài hước, dí dỏm.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 2783,
    group: 5,
  },
  {
    id: 23,
    title: 'hệ thống',
    slug: 'he-thong',
    description:
      'Truyện nhân vật chính có hệ thống hỗ trợ giúp họ thăng cấp, mạnh lên nhanh chóng.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1221,
    group: 2,
  },
  {
    id: 24,
    title: 'khoa huyễn',
    slug: 'khoa-huyen',
    description:
      'Truyện viễn tưởng với công nghệ tiên tiến, du hành vũ trụ, trí tuệ nhân tạo.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 624,
    group: 2,
  },
  {
    id: 25,
    title: 'khác',
    slug: 'khac',
    description:
      'Các thể loại không nằm trong danh sách chính, đa dạng phong cách.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 6758,
    group: 5,
  },
  {
    id: 27,
    title: 'kiếm hiệp',
    slug: 'kiem-hiep',
    description:
      'Truyện về thế giới giang hồ, cao thủ võ lâm, tranh đoạt bí kíp, ân oán giang hồ.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 969,
    group: 4,
  },
  {
    id: 31,
    title: 'linh dị',
    slug: 'linh-di',
    description:
      'Truyện có yếu tố tâm linh, ma quỷ, thần tiên, linh hồn và thế giới bên kia.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1314,
    group: 2,
  },
  {
    id: 32,
    title: 'lịch sử',
    slug: 'lich-su',
    description:
      'Truyện tái hiện bối cảnh và nhân vật qua các giai đoạn lịch sử.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 267,
    group: 3,
  },
  {
    id: 33,
    title: 'mạt thế',
    slug: 'mat-the',
    description:
      'Truyện về tận thế, dịch bệnh, zombie, chiến tranh hủy diệt, nhân loại sụp đổ.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 488,
    group: 4,
  },
  {
    id: 35,
    title: 'ngôn tình',
    slug: 'ngon-tinh',
    description:
      'Truyện tình cảm nam nữ, tập trung vào cảm xúc, mối quan hệ giữa các nhân vật.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 16792,
    group: 1,
  },
  {
    id: 36,
    title: 'ngược',
    slug: 'nguoc',
    description:
      'Truyện có tình tiết đau khổ, bi thương, nhân vật chính chịu nhiều tổn thương.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 2368,
    group: 1,
  },
  {
    id: 37,
    title: 'nữ cường',
    slug: 'nu-cuong',
    description:
      'Truyện có nữ chính mạnh mẽ, độc lập, không phụ thuộc vào nam chính.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 2208,
    group: 1,
  },
  {
    id: 38,
    title: 'nữ phụ',
    slug: 'nu-phu',
    description:
      'Truyện lấy góc nhìn của nhân vật phụ là nữ, thường cạnh tranh với nữ chính.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 534,
    group: 1,
  },
  {
    id: 40,
    title: 'phương tây',
    slug: 'phuong-tay',
    description: 'Truyện lấy bối cảnh hoặc văn hóa phương Tây, Âu – Mỹ.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 999,
    group: 3,
  },
  {
    id: 42,
    title: 'quan trường',
    slug: 'quan-truong',
    description:
      'Truyện xoay quanh đấu đá quyền lực trong triều đình, quan trường.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 51,
    group: 3,
  },
  {
    id: 44,
    title: 'quân sự',
    slug: 'quan-su',
    description:
      'Truyện xoay quanh chiến tranh, binh pháp, quân đội, vũ khí hiện đại hoặc cổ trang.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 208,
    group: 3,
  },
  {
    id: 46,
    title: 'sắc',
    slug: 'sac',
    description: 'Truyện chứa yếu tố gợi cảm, tình dục rõ nét.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 27,
    group: 1,
  },
  {
    id: 47,
    title: 'sủng',
    slug: 'sung',
    description: 'Truyện ngọt ngào, nhân vật chính bị chiều chuộng tuyệt đối.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 5864,
    group: 1,
  },
  {
    id: 48,
    title: 'thám hiểm',
    slug: 'tham-hiem',
    description: 'Truyện nhân vật khám phá vùng đất mới, bí ẩn.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 215,
    group: 4,
  },
  {
    id: 50,
    title: 'tiên hiệp',
    slug: 'tien-hiep',
    description:
      'Truyện về con đường tu luyện tiên đạo, phi thăng thành tiên, chiến đấu với yêu ma.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1371,
    group: 2,
  },
  {
    id: 52,
    title: 'trinh thám',
    slug: 'trinh-tham',
    description:
      'Truyện điều tra phá án, khám phá bí ẩn, các vụ án giết người hoặc âm mưu lớn.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 802,
    group: 4,
  },
  {
    id: 57,
    title: 'trọng sinh',
    slug: 'trong-sinh',
    description:
      'Nhân vật quay về quá khứ với ký ức kiếp trước để thay đổi số mệnh.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 3051,
    group: 2,
  },
  {
    id: 62,
    title: 'võng du',
    slug: 'vong-du',
    description:
      'Truyện liên quan đến game online, nhân vật chính phát triển trong thế giới ảo.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 470,
    group: 2,
  },
  {
    id: 64,
    title: 'xuyên không',
    slug: 'xuyen-khong',
    description:
      'Truyện nhân vật chính bị đưa đến thời gian hoặc thế giới khác, có thể cổ đại hoặc tương lai.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 5827,
    group: 2,
  },
  {
    id: 65,
    title: 'xuyên nhanh',
    slug: 'xuyen-nhanh',
    description: 'Xuyên không thời gian ngay lập tức, ít gián đoạn chuyển đổi.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 531,
    group: 2,
  },
  {
    id: 67,
    title: 'đam mỹ',
    slug: 'dam-my',
    description:
      'Truyện tình cảm giữa hai nhân vật nam, thể hiện tình yêu và cảm xúc chân thành.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 8756,
    group: 1,
  },
  {
    id: 68,
    title: 'điền văn',
    slug: 'dien-van',
    description: 'Truyện nông thôn, đồng quê, cuộc sống miền yên bình.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1488,
    group: 1,
  },
  {
    id: 69,
    title: 'đoản văn',
    slug: 'doan-van',
    description: 'Truyện ngắn, thường chỉ vài chương, nội dung cô đọng.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 1778,
    group: 1,
  },
  {
    id: 70,
    title: 'đô thị',
    slug: 'do-thi',
    description:
      'Truyện lấy bối cảnh thành phố, cuộc sống hiện đại, xoay quanh kinh doanh, xã hội.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 5979,
    group: 5,
  },
  {
    id: 71,
    title: 'đông phương',
    slug: 'dong-phuong',
    description: 'Truyện lấy bối cảnh Trung Quốc hoặc văn hóa Á Đông.',
    image: '',
    is_block: false,
    is_hidden: false,
    quantity: 172,
    group: 3,
  },
];
