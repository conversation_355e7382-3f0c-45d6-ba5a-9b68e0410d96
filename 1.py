import re
import os

# Regex: selector: '[app-...]'
pattern_selector = re.compile(r"selector:\s*'\[app-([a-z0-9-]+)\]'", re.IGNORECASE)


def process_file(path: str):
    with open(path, "r", encoding="utf-8") as f:
        content = f.read()

    new_content = pattern_selector.sub(r"selector: 'main[app-\1]'", content)

    if new_content != content:
        with open(path, "w", encoding="utf-8") as f:
            f.write(new_content)
        print(f"Updated: {path}")


def process_directory(root_dir: str):
    for root, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith(".ts"):  # chỉ xử lý file TS
                process_file(os.path.join(root, file))


if __name__ == "__main__":
    # 👉 Chỉnh path cho đúng folder bạn muốn (ví dụ: ./src/app/components)
    folder = "./src/app/pages"
    
    process_directory(folder)
