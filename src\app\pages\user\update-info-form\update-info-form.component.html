<div
  class="col-span-2 bg-white dark:bg-dark-700 max-h-[80vh] flex flex-col overflow-hidden rounded-lg"
>
  <div
    class="p-4 border-b border-gray-200 dark:border-neutral-600 text-xl font-semibold dark:text-white"
  >
    Chỉnh sửa thông tin
  </div>
  <div class="flex-1 overflow-y-auto p-4">
    <div class="mb-4">
      <h3 class="font-semibold dark:text-white text-start">Giới thiệu</h3>
      <textarea
        [(ngModel)]="maxim"
        [textContent]="maxim"
        name=""
        id=""
        class="w-full border dark:border-neutral-600 dark:text-white dark:bg-neutral-700 py-2 px-3"
      ></textarea>
      <button
        [ngClass]="{ hidden: !maxim }"
        (click)="onUpdateMaxim(maxim)"
        class="bg-primary-100 w-full text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
        type="button"
        aria-label="Lưu"
      >
        Lưu
      </button>
    </div>

    <div
      class="p-4 mb-4 border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6"
    >
      <h3 class="mb-4 font-semibold dark:text-white text-start">
        Thông tin chung
      </h3>
      <form
        [formGroup]="infoForm"
        (ngSubmit)="$event.preventDefault(); onUpdateInfo()"
      >
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-6 sm:col-span-3">
            <label
              for="first-name"
              class="block mb-2 font-medium text-gray-900 dark:text-white"
            >
              <div class="flex-start gap-1">
                <svg
                  class="h-6 w-6"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <circle cx="12" cy="12" r="9" />
                  <line x1="9" y1="10" x2="9.01" y2="10" />
                  <line x1="15" y1="10" x2="15.01" y2="10" />
                  <path d="M9.5 15a3.5 3.5 0 0 0 5 0" />
                </svg>

                <span>Tên</span>
              </div>
            </label>
            <input
              type="text"
              name="first-name"
              id="first-name"
              class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
              placeholder="Bonnie"
              required=""
              formControlName="firstName"
            />
            <div
              *ngIf="
                (infoForm.get('firstName')?.invalid &&
                  infoForm.get('firstName')?.dirty) ||
                infoForm.get('firstName')?.touched ||
                submitInfoFailed
              "
            >
              <small
                *ngIf="infoForm.get('firstName')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập Tên người dùng</small
              >
            </div>
          </div>
          <div class="col-span-6 sm:col-span-3">
            <label
              for="last-name"
              class="block mb-2 font-medium text-gray-900 dark:text-white"
            >
              <div class="flex-start gap-1">
                <svg
                  class="h-6 w-6"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <circle cx="12" cy="7" r="4" />
                  <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                </svg>
                <span>Họ</span>
              </div>
            </label>
            <input
              type="text"
              name="last-name"
              id="last-name"
              class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 placeholder-gray-200 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
              required=""
              formControlName="lastName"
            />
            <div
              *ngIf="isControlInvalid('lastName', infoForm) || submitInfoFailed"
            >
              <small
                *ngIf="infoForm.get('lastName')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập Họ người dùng
              </small>
            </div>
          </div>

          <div class="col-span-6">
            <label
              for="email"
              class="block mb-2 font-medium text-gray-900 dark:text-white"
            >
              <div class="flex-start gap-1">
                <svg
                  class="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>

                <span>Email</span>
              </div>
            </label>
            <input
              type="email"
              name="email"
              id="email"
              class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
              placeholder="<EMAIL>"
              required=""
              formControlName="email"
            />
            <div
              *ngIf="isControlInvalid('email', infoForm) || submitInfoFailed"
            >
              <small
                *ngIf="infoForm.get('email')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập email người dùng
              </small>
              <small
                *ngIf="infoForm.get('email')?.hasError('email')"
                class="text-red-400"
                >Email không chính xác
              </small>
            </div>
          </div>

          <div class="col-span-6">
            <label
              for="birthday"
              class="block mb-2 font-medium text-gray-900 dark:text-white"
            >
              <div class="flex-start gap-1">
                <svg
                  class="h-6 w-6"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="currentColor"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <rect x="4" y="5" width="16" height="16" rx="2" />
                  <line x1="16" y1="3" x2="16" y2="7" />
                  <line x1="8" y1="3" x2="8" y2="7" />
                  <line x1="4" y1="11" x2="20" y2="11" />
                  <rect x="8" y="15" width="2" height="2" />
                </svg>

                <span>Ngày sinh</span>
              </div>
            </label>
            <input
              type="date"
              name="birthday"
              id="birthday"
              class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
              placeholder="15/08/1990"
              required=""
              formControlName="dob"
            />
            <div *ngIf="isControlInvalid('dob', infoForm) || submitInfoFailed">
              <small
                *ngIf="infoForm.get('dob')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập năm sinh
              </small>
            </div>
          </div>

          <div class="col-span-6" [ngClass]="{ hidden: infoForm.invalid }">
            <button
              class="bg-primary-100 w-full text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              type="submit"
              aria-label="Lưu thông tin"
            >
              Lưu
            </button>
          </div>
        </div>
      </form>
    </div>
    <div
      class="p-4 mb-4 border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-neutral-700 sm:p-6"
    >
      <h3 class="mb-4 font-semibold dark:text-white text-start">
        Đổi mật khẩu
      </h3>
      <form [formGroup]="passwordForm" (ngSubmit)="onUpdatePassword()">
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-6 sm:col-span-3">
            <label
              for="current-password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white text-start"
              >Mật khẩu hiện tại</label
            >
            <div class="mt-1 relative">
              <input
                [type]="showPassword ? 'text' : 'password'"
                name="current-password"
                id="current-password"
                class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                placeholder="••••••••"
                required=""
                formControlName="oldPassword"
              />
            </div>
            <div
              *ngIf="
                isControlInvalid('oldPassword', passwordForm) || submitFailed
              "
            >
              <small
                *ngIf="passwordForm.get('oldPassword')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập mật khẩu hiện tại</small
              >
            </div>
          </div>
          <div class="col-span-6 sm:col-span-3">
            <label
              for="password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white text-start"
              >Mật khẩu mới</label
            >
            <div class="mt-1 relative">
              <input
                data-popover-target="popover-password"
                data-popover-placement="bottom"
                [type]="showNewPassword ? 'text' : 'password'"
                id="password"
                class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-100 focus:border-primary-100 block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-100 dark:focus:border-primary-100"
                placeholder="••••••••"
                required=""
                formControlName="newPassword"
              />
            </div>
            <div
              *ngIf="
                isControlInvalid('newPassword', passwordForm) || submitFailed
              "
            >
              <small
                *ngIf="passwordForm.get('newPassword')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập mật khẩu mới</small
              >
            </div>
            <div
              data-popover=""
              id="popover-password"
              role="tooltip"
              class="absolute z-10 invisible inline-block text-sm font-light text-gray-500 transition-opacity duration-300 border border-gray-200 rounded-lg shadow-sm opacity-0 w-72 dark:border-neutral-500 dark:text-gray-400"
              style="
                position: absolute;
                inset: auto auto 0px 0px;
                margin: 0px;
                transform: translate3d(510.4px, -1755.2px, 0px);
              "
              data-popper-placement="top"
              data-popper-reference-hidden=""
              data-popper-escaped=""
            >
              <div class="p-3 space-y-2">
                <h3 class="font-semibold text-gray-900 dark:text-white">
                  Must have at least 6 characters
                </h3>
                <div class="grid grid-cols-4 gap-2">
                  <div class="h-1 bg-orange-300 dark:bg-orange-400"></div>
                  <div class="h-1 bg-orange-300 dark:bg-orange-400"></div>
                  <div class="h-1 bg-gray-200 dark:bg-gray-600"></div>
                  <div class="h-1 bg-gray-200 dark:bg-gray-600"></div>
                </div>
                <p>It’s better to have:</p>
                <ul>
                  <li class="flex-start mb-1">
                    <svg
                      class="w-4 h-4 mr-2 text-green-400 dark:text-green-500"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    Upper &amp; lower case letters
                  </li>
                  <li class="flex-start mb-1">
                    <svg
                      class="w-4 h-4 mr-2 text-gray-300 dark:text-gray-400"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    A symbol (#$&amp;)
                  </li>
                  <li class="flex-start">
                    <svg
                      class="w-4 h-4 mr-2 text-gray-300 dark:text-gray-400"
                      aria-hidden="true"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    A longer password (min. 12 chars.)
                  </li>
                </ul>
              </div>
              <div
                data-popper-arrow=""
                style="
                  position: absolute;
                  left: 0px;
                  transform: translate3d(139.2px, 0px, 0px);
                "
              ></div>
            </div>
          </div>
          <div class="col-span-6 sm:col-span-3">
            <label
              for="confirm-password"
              class="block mb-2 text-sm font-medium text-gray-900 dark:text-white text-start"
              >Nhập lại mật khẩu mới</label
            >
            <div class="mt-1 relative">
              <input
                [type]="showRePassword ? 'text' : 'password'"
                name="confirm-password"
                id="confirm-password"
                class="shadow-sm border border-gray-300 text-gray-900 sm:text-sm rounded-lg block w-full p-2.5 dark:bg-neutral-700 dark:border-neutral-500 dark:placeholder-gray-400 dark:text-white"
                placeholder="••••••••"
                required=""
                formControlName="rePassword"
              />
            </div>
            <div
              *ngIf="
                isControlInvalid('rePassword', passwordForm) || submitFailed
              "
            >
              <small
                *ngIf="passwordForm.get('rePassword')?.hasError('required')"
                class="text-red-400"
                >Vui lòng nhập lại mật khẩu</small
              >
            </div>
          </div>
          <div
            class="col-span-6 sm:col-full"
            [ngClass]="{ hidden: passwordForm.invalid }"
          >
            <button
              class="bg-primary-100 text-white w-full hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              type="submit"
              aria-label="Lưu mật khẩu"
            >
              Lưu
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
