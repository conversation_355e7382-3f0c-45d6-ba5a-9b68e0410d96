<div class="flex justify-between my-2">
  <span class="inline-flex items-center gap-2">
    <svg
      class="size-6"
      focusable="false"
      viewBox="0 0 24 24"
      fill="currentColor"
      data-testid="LibraryBooksIcon"
    >
      <path
        d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"
      ></path>
    </svg>
    <h2 class="text-xl font-semibold uppercase">{{ title }}</h2>
  </span>
  <div
    app-select
    *ngIf="options.length > 0"
    [selectedValue]="-1"
    [options]="this.options"
    (selectedValueChange)="selectedValueChange.emit($event)"
    [size]="'small'"
    class="w-32"
  ></div>
</div>
<div
  class="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 grid-rows-1 gap-2 md:gap-3 my-4"
>
  <ng-container *ngFor="let novel of novels">
    <div app-card-v1 [novel]="novel"></div>
  </ng-container>
</div>
