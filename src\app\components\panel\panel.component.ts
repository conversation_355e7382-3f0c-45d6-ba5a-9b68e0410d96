import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  ElementRef,
  Input,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';

@Component({
  selector: '[app-panel]',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './panel.component.html',
  styleUrl: './panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    class: 'bg-neutral-200 dark:bg-dark-600 h-full w-full shadow-common rounded-2xl overflow-hidden relative flex flex-col',
  },
})
export class PanelComponent {
  @Input() title = '';
  @ContentChild('iconTemplate') iconTemplate: TemplateRef<HTMLElement> | null =
    null;
  @ViewChild('paneltitle') paneltitle: ElementRef<HTMLElement> | null = null;
  ngOnInit() {}
  ngAfterViewInit() {}
}
