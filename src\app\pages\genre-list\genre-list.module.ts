import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { GenreListComponent } from './genre-list.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';

const routes: Routes = [
  {
    path: '',
    component: GenreListComponent,
  },
];

@NgModule({
  declarations: [GenreListComponent],
  imports: [CommonModule, RouterModule.forChild(routes), BreadcrumbComponent, ImgLayoutComponent],
})
export class GenreListModule {}
