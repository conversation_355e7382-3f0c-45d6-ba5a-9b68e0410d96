import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { RouterModule } from '@angular/router';
import { PopupComponent } from '@components/popup/popup.component';
import { fadeInOut } from '@components/utils/animation';
import { Genre } from '@schemas/Genre';
import { NovelService } from '@services/novel.service';

@Component({
  selector: '[app-genres]',
  standalone: true,
  templateUrl: './genres.component.html',
  styleUrl: './genres.component.scss',
  imports: [CommonModule, RouterModule],
  animations: [fadeInOut],
  encapsulation: ViewEncapsulation.None,
})
export class GenresComponent extends PopupComponent {
  genres?: Record<string, Genre[]>;
  @Input() routerLinkGenres: boolean = false;
  @Input() statusGenres: any = {};
  @Output() genreSelected: EventEmitter<Genre> = new EventEmitter<Genre>();
  genreHovered?: Genre;
  filteredGenres?: Record<string, Genre[]>;
  searchTerm = '';
  groupGenres = [
    {
      id: 1,
      title: 'Tình cảm & Lãng mạn',
    },
    {
      id: 2,
      title: 'Huyền ảo & Tu chân',
    },
    {
      id: 3,
      title: 'Cung đình & Lịch sử',
    },
    {
      id: 4,
      title: 'Hành động & Phiêu lưu',
    },
    {
      id: 5,
      title: 'Hiện đại & Đời thường',
    },
  ];

  constructor(
    public override ref: ElementRef<HTMLElement>,
    private novelService: NovelService,
  ) {
    super(ref);
    this.novelService.getGenres().subscribe((res) => {
      this.genres = res.reduce((acc: Record<string, Genre[]>, item: Genre) => {
        const grp = item.group;
        if (!acc[grp]) {
          acc[grp] = [];
        }
        acc[grp].push(item);
        return acc;
      }, {});
    });
    this.filteredGenres = this.genres;
  }

  filterGenres(value: string): void {
    this.searchTerm = value;
    if (!value) {
      this.filteredGenres = this.genres;
      return;
    }

    const filtered = Object.entries(this.genres || {}).filter(
      ([category, genres]) => {
        const filteredGenres = genres.filter((genre) =>
          genre.title.toLowerCase().includes(value.toLowerCase()),
        );

        return filteredGenres.length > 0;
      },
    );
    this.filteredGenres = Object.fromEntries(filtered);
  }

  public onSelectGenre(genre: Genre): void {
    this.genreSelected.emit(genre);
  }
}
