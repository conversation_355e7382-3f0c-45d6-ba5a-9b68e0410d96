import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  QueryList,
  ViewChildren,
  ViewEncapsulation,
} from '@angular/core';
// import { OverlayContainer } from '@angular/cdk/overlay';

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Comment } from '@schemas/Comment';

import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { DOCUMENT } from '@angular/common';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { PopupService } from '@services/popup.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
  selector: '[app-comment]',
  templateUrl: './comment.component.html',
  styleUrls: ['./comment.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('inOutAnimation', [
      state('in', style({ opacity: 1 })),
      transition(':enter', [
        animate(
          1000,
          keyframes([
            style({ opacity: 0, offset: 0 }),
            style({ opacity: 0.25, offset: 0.25 }),
            style({ opacity: 0.5, offset: 0.5 }),
            style({ opacity: 0.75, offset: 0.75 }),
            style({ opacity: 1, offset: 1 }),
          ]),
        ),
      ]),
    ]),
  ],
  standalone: false,
})
export class CommentComponent implements OnInit, OnChanges {
  formComment!: FormGroup;
  formReply!: FormGroup;
  @Input()
  novelId!: number;
  listComments: Comment[] = [];
  lenghtComments = 0;
  replyId = -1;
  isViewreply = false;
  isLogin!: boolean;
  BeginState = true;
  selectedEmojis: string[] = [];

  currentPage = 1;
  totalpage = 0;
  // @ViewChild(DynamicHostDirective, { static: true }) dynamicHost!: DynamicHostDirective;
  @ViewChildren('ViewReplyEle') ViewReplyEles!: QueryList<ElementRef>;
  isTyping = false;
  user?: User;
  levelInfoUser!: { percent: number; level: string; nextLevel: string };
  constructor(
    private formBuilder: FormBuilder,
    private accountService: AccountService,
    private myElement: ElementRef,
    private toastService: ToastService,
    private popupService: PopupService,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.formComment = this.formBuilder.group({
      content: ['', Validators.required],
    });
    this.formReply = this.formBuilder.group({
      content: ['', Validators.required],
      replyfromUser: [-1, Validators.required],
    });
  }
  ngOnInit(): void {
    this.lenghtComments = 0;
    this.isLogin = this.accountService.isAuthenticated();
  }

  WindowScroll = (event: any) => {
    let ScreenBottomOffset =
      (this.document?.defaultView?.scrollY || 0) +
      (this.document?.defaultView?.innerHeight || 0);

    if (
      ScreenBottomOffset >= this.myElement.nativeElement.offsetTop &&
      this.BeginState
    ) {
      this.BeginState = false;
      this.refreshcomments();
      this.user = this.accountService.GetUser();
      this.document.removeEventListener('scroll', this.WindowScroll);
    }
  };

  refreshcomments() {
    this.listComments = [];
    this.accountService
      .GetCommentsByNovelId(this.novelId, this.currentPage, 10)
      .subscribe((res: any) => {
        this.lenghtComments = 0;
        this.totalpage = res.data.totalpage;
        this.listComments = res.data.comments;

        this.listComments.forEach((comment: any) => {
          this.lenghtComments += comment.replies.length;
        });
        this.lenghtComments += this.listComments.length;
      });
  }

  onSubmit(form: FormGroup, replyfromCmt: number | undefined = undefined) {
    if (!form.valid) return;
    if (!this.isLogin) {
      form.reset();
      this.toastService.show(
        ToastType.Error,
        `Bạn hãy <a class="text-blue-400 hover:underline">đăng nhập</a> để bình luận truyện nhé!`,
      );
      return;
    }
    let userId = form.value.replyfromUser ?? null;
    this.accountService
      .AddComment(this.novelId, form.value.content, userId, replyfromCmt)
      .subscribe((res: any) => {
        if (res.status === 1) {
          res.data.avatar = this.user?.avatar;

          if (!replyfromCmt) {
            this.listComments.unshift(res.data);
          } else {
            let parentComment = this.listComments.find(
              (comment: any) => comment.id === replyfromCmt,
            );
            if (parentComment) {
              if (parentComment.replies) parentComment.replies.push(res.data);
              else parentComment.replies = [res.data];
            }
          }
          this.lenghtComments += 1;
        }
      });
    form.reset();
  }
  replyCmt(comment: Comment, hide: boolean) {
    if (!this.isLogin) {
      this.toastService.show(
        ToastType.Error,
        'Vui lòng đăng nhập để phản hồi bình luận',
      );
      return;
    }
    this.formReply.reset();
    let commentId = comment.id;
    this.isViewreply = false;
    let content = '';
    if (comment.userID !== this.user?.id) {
      content = `@${comment.userName} `;
    }
    this.formReply.setValue({ content, replyfromUser: comment.id });

    if (!hide) {
      this.replyId = commentId;
      let El = this.ViewReplyEles.find(
        (element) =>
          element.nativeElement.getAttribute('reply-block') ===
          commentId.toString(),
      );
      El?.nativeElement.classList.remove('h-0');
    } else {
      let El = this.ViewReplyEles.find(
        (element) =>
          element.nativeElement.querySelector(`#id${commentId}`) !== null,
      );
      this.replyId = parseInt(El?.nativeElement.getAttribute('reply-block'));
      El?.nativeElement.classList.remove('h-0');
    }
  }
  ViewReplyCmt(commentId: number) {
    if (this.replyId === commentId) {
      this.isViewreply = !this.isViewreply;
    }

    let El = this.ViewReplyEles.find(
      (element) =>
        element.nativeElement.getAttribute('reply-block') ===
        commentId.toString(),
    );
    El?.nativeElement.classList.toggle('h-0');
  }
  ViewInfoUser(userID: number) {
    // this.popupService.showUserInfo({ userID });
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(event: Event) {
    if ((event.target as Document).defaultView?.scrollY === 0) {
      this.BeginState = true;
    }
  }
  onInput(form: FormGroup): void {
    this.isTyping = form.get('content')?.value.length > 0;
  }
  ngOnChanges(change: any) {
    this.document.removeEventListener('scroll', this.WindowScroll);
    this.document.addEventListener('scroll', this.WindowScroll);
  }

  OnChangePage(page: number) {
    this.currentPage = page;
    this.refreshcomments();
  }
}
