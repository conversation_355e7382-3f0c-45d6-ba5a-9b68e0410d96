import { InputType, SettingOption } from '@schemas/SettingOption';
import { SettingType } from '@schemas/SettingType.enum';
import { VOICE } from './constants';

export const themeSetting: SettingOption = {
  inputType: InputType.Selection,
  type: SettingType.Theme,
  options: [
    { label: 'Light', value: 'light' },
    { label: 'Dark', value: 'dark' },
  ],
  value: 'light',
  name: 'Chủ đề',
  description: 'Chọn giao diện sáng hoặc tối cho website',
  group: 1,
};

export const languageSetting: SettingOption = {
  inputType: InputType.Selection,

  type: SettingType.Language,
  options: [{ label: 'Tiếng Việt', value: 'vi-VN' }],
  value: 'vi-VN',
  name: 'Ngôn ngữ',
  description: 'Ngôn ngữ hiển thị cho website',
  group: 1,
};

export const primaryColorSetting: SettingOption = {
  inputType: InputType.Color,
  type: SettingType.PrimaryColor,
  options: [
    {
      label: 'Đỏ',
      value: '#e35f66',
      colors: { '100': '#e35f66', '200': '#c03f4b', '300': '#9d1d31' },
    },
    {
      label: 'Nâu đỏ ấm (nhẹ nhàng, cổ điển)',
      value: '#8C4A2F',
      colors: { '100': '#8C4A2F', '200': '#8C4A2F', '300': '#8C4A2F' },
    },
    {
      label: 'Xanh pastel (biển nhạt)',
      value: '#63B3ED',
      colors: { '100': '#63B3ED', '200': '#63B3ED', '300': '#63B3ED' },
    },
  ],
  value: '#e35f66',
  name: 'Màu chủ đạo',
  description: 'Màu chủ đạo',
  group: 1,
};
export const bgColorSetting: SettingOption = {
  inputType: InputType.Color,
  type: SettingType.BgColor,
  options: [
    {
      label: 'Trắng',
      value: '#f9f9f9',
      colors: { '100': '#f9f9f9', '200': '#f9f9f9', '300': '#f9f9f9' },
    },
    {
      label: 'Kem',
      value: '#FFF8E1',
      colors: { '100': '#FFF8E1', '200': '#FFF8E1', '300': '#FFF8E1' },
    },
  ],
  value: '#f9f9f9',
  name: 'Màu nền',
  description: 'Màu nền',
  group: 1,
};
export const fixedHeaderSetting: SettingOption = {
  inputType: InputType.Toggle,
  type: SettingType.FixedHeader,
  value: false,
  name: 'Header cố định khi cuộn',
  description: 'Header cố định khi cuộn',
  group: 1,
};

export const fontSetting: SettingOption = {
  inputType: InputType.Selection,

  type: SettingType.FontFamily,
  options: [
    { label: 'Arial', value: `'Arial', sans-serif` },
    { label: 'Be Vietnam Pro', value: `'Be Vietnam Pro', sans-serif` },
    { label: 'Times New Roman', value: `'Times New Roman', serif` },
    { label: 'Roboto', value: `'Roboto', sans-serif` },
    { label: 'Palatino Linotype', value: `'Palatino Linotype', serif` },
    { label: 'Source Sans Pro', value: `'Source Sans Pro', sans-serif` },
    { label: 'Segoe UI', value: `'Segoe UI', Tahoma, Geneva, sans-serif` },
    { label: 'Patrick Hand', value: `'Patrick Hand', cursive` },
    { label: 'Noticia Text', value: `'Noticia Text', serif` },
    { label: 'Verdana', value: `'Verdana', Geneva, sans-serif` },
    { label: 'Tahoma', value: `'Tahoma', Geneva, sans-serif` },
  ],
  value: `'Be Vietnam Pro', sans-serif`,
  name: 'Font chữ',
  description: 'Lựa chọn font chữ khi đọc.',
  group: 2,
};
export const fontSizeSetting: SettingOption = {
  type: SettingType.FontSize,
  inputType: InputType.Range,
  min: 10,
  max: 50,
  step: 1,
  value: 18,
  name: 'Cỡ chữ',
  description: 'Cỡ chữ',
  group: 2,
};

export const textureSetting: SettingOption = {
  inputType: InputType.Selection,
  type: SettingType.Texture,
  options: [
    { label: 'Mặc định', value: '' },
    { label: 'Giấy', value: 'textures/paper-texture.jpg' },
    { label: 'Gỗ', value: 'textures/wood-texture.png' },
    { label: 'Nhám', value: 'textures/rough-texture.jpg' },
    { label: 'Xám', value: 'textures/smoke-texture.jpg' },
  ],
  value: '',
  name: 'Chất liệu',
  description: 'Chọn hiệu ứng chất liệu cho nền trang.',
  group: 2,
};
export const lineHeightSetting: SettingOption = {
  inputType: InputType.Selection,
  type: SettingType.LineHeight,
  options: [
    { label: '1', value: 1 },
    { label: '1.5', value: 1.5 },
    { label: '2', value: 2 },
    { label: '2.5', value: 2.5 },
  ],
  value: 1.5,
  name: 'Giãn dòng',
  description: 'Điều chỉnh khoảng cách giữa các dòng.',
  group: 2,
};

export const scrollSpeedSetting: SettingOption = {
  type: SettingType.ScrollSpeed,
  inputType: InputType.Range,
  min: 1,
  max: 10,
  step: 1,
  value: 3,
  name: 'Tốc độ cuộn',
  description: 'Tùy chỉnh tốc độ cuộn trang khi đọc.',
  group: 2,
};
export const fixedToolbarSetting: SettingOption = {
  inputType: InputType.Toggle,
  type: SettingType.FixedToolbar,
  value: true,
  name: 'Thanh công cụ cố định khi cuộn',
  description: 'Thanh công cụ cố định khi cuộn',
  group: 2,
};

export const voiceSetting: SettingOption = {
  type: SettingType.Voice,
  inputType: InputType.Selection,
  options: [
    { label: 'Google', value: VOICE.GG },
    { label: 'Nam Minh (VIP)', value: VOICE.MALEEGE },
    { label: 'Hoài My (VIP)', value: VOICE.FEMALEege },
  ],
  value: VOICE.GG,
  name: 'Giọng đọc',
  description: 'Giọng đọc khi phát audio.',
  group: 3,
};
export const volumeSetting: SettingOption = {
  type: SettingType.Volume,
  inputType: InputType.Range,
  min: 0,
  max: 100,
  step: 1,
  value: 100,
  name: 'Âm lượng',
  description: 'Điều chỉnh mức âm lượng khi phát audio.',
  group: 3,
};
export const audioSpeedSetting: SettingOption = {
  type: SettingType.AudioSpeed,
  inputType: InputType.Selection,
  options: [
    { label: '0.5', value: 0.5 },
    { label: '0.75', value: 0.75 },
    { label: '1', value: 1 },
    { label: '1.25', value: 1.25 },
    { label: '1.5', value: 1.5 },
    { label: '1.75', value: 1.75 },
    { label: '2', value: 2 },
  ],
  value: 1,
  name: 'Tốc độ phát',
  description: 'Chọn tốc độ phát lại audio.',
  group: 3,
};

export const autoNextAudioSetting: SettingOption = {
  inputType: InputType.Toggle,
  type: SettingType.AutoNextChapter,
  value: true,
  name: 'Tự động chuyển chương',
  description: 'Tự động chuyển sang chương tiếp theo khi audio kết thúc.',
  group: 3,
};
export const autoZoomOutSetting: SettingOption = {
  inputType: InputType.Toggle,
  type: SettingType.AutoZoomAudio,
  value: false,
  name: 'Tự động thu nhỏ',
  description: 'Tự động thu nhỏ click vào bên ngoài audio.',
  group: 3,
};
