<div
  app-img-layout
  [title]="'Dịch Vụ Premium'"
  [description]="'<PERSON><PERSON>g cấp trải nghiệm đọc truyện với các gói dịch vụ cao cấp'"
>
  <div
    app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Dịch vụ', url: '/price' },
      { label: tabs[selectedTab].name, url: tabs[selectedTab].routerLink },
    ]"
  ></div>
  <div
    app-tabs
    [tabs]="tabs"
    [selectedTab]="selectedTab"
    (selectedTabChange)="onTabChange($event)"
  ></div>

  <router-outlet></router-outlet>
</div>
