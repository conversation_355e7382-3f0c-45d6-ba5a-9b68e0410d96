import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  Input,
  PLATFORM_ID,
  SimpleChange,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { LoopScrollComponent } from '@components/loop-scroll/loop-scroll.component';
import { PopupComponent } from '@components/popup/popup.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { fadeInOut } from '@components/utils/animation';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { ChapterPage } from '@schemas/ChapterPage';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';

@Component({
  selector: '[app-chapter-popup]',
  standalone: true,
  templateUrl: './chapter-popup.component.html',
  styleUrl: './chapter-popup.component.scss',
  animations: [fadeInOut],
  imports: [CommonModule, RouterLink, LoopScrollComponent, SpinnerComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterPopupComponent extends PopupComponent {
  @Input() chapterPage?: ChapterPage;
  filteredChapters: Chapter[] = [];
  allchapters: Chapter[] = [];
  isLoading = false;
  constructor(
    private novelService: NovelService,
    private elementRef: ElementRef,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {
    super(elementRef);
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.filteredChapters = this.allchapters;
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes['visible'] && changes['visible'].currentValue) {
      this.open();
    }
  }

  override open(): void {
    super.open();
    this.isLoading = true;
    this.novelService
      .getChapters(this.chapterPage!.novel!.id, 1, 99999)
      .subscribe((res: IServiceResponse<ChapterList>) => {
        if (!res.data) return;
        this.allchapters = res.data.chapters;
        this.allchapters.sort((a, b) => a.id - b.id);
        this.filteredChapters = this.allchapters;
        this.isLoading = false;
        this.cd.detectChanges();
      });
  }

  filterChapters(searchString: string) {
    this.filteredChapters = this.allchapters.filter((chapter) => {
      return chapter.title!.toLowerCase().includes(searchString.toLowerCase());
    });
  }
}
