import { CommonModule } from '@angular/common';
import { Component, EventEmitter } from '@angular/core';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
  standalone: true,
  imports: [CommonModule],
  selector: '[app-confirm-popup]',
  templateUrl: './confirm-popup.component.html',
  styleUrl: './confirm-popup.component.scss',
})
export class ConfirmPopupComponent implements IPopupComponent {
  isOpen = false;
  title = '';
  message = '';
  confirmButtonText = '';
  cancelButtonText = '';

  confirm = new EventEmitter<void>();
  cancel = new EventEmitter<void>();
  handleConfirm() {
    this.confirm.emit();
    this.isOpen = false;
  }

  handleCancel() {
    this.cancel.emit();
    this.isOpen = false;
  }

  async show(o: any) {
    const { title, message, confirmButtonText, cancelButtonText } = o;
    this.isOpen = true;
    this.title = title;
    this.message = message;
    this.confirmButtonText = confirmButtonText;
    this.cancelButtonText = cancelButtonText;
    return await new Promise((resolve) => {
      //wait until handleConfirm
      const confirmSubscription = this.confirm.subscribe(() => {
        confirmSubscription.unsubscribe();
        resolve({ isconfirm: true, isCancel: false });
      });
      const cancelSubscription = this.cancel.subscribe(() => {
        cancelSubscription.unsubscribe();
        resolve({ isconfirm: false, isCancel: true });
      });
    });
  }
}
