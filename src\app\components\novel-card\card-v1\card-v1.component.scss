.card {
  .card-body {
    display: flex;
    flex-direction: column;

    .details {
      height: 20%;
    }

    // Ensure the image takes up 80% of the remaining space
    img {
      flex: 1;
      height: 100%;
      object-fit: cover; // Ensure the image covers the entire container
    }
  }
}

.tag-hot {
  @apply bg-primary-200 m-1 w-1/4 h-5 transform rounded-md absolute top-0 right-0 z-0 text-center flex-center;
}

.card-footer-v1 {
  @apply mt-1 flex gap-1 lg:gap-2;
}

.chapter {
  @apply font-semibold line-clamp-1;
}

.chapter-update {
  @apply text-end text-[0.75rem] inline text-gray-700 dark:text-gray-400;
}

.details {
  @apply flex text-gray-500 space-x-8;
}

.placeholder-image {
  @apply animate-pulse bg-gray-300 dark:bg-neutral-700 relative w-full h-0 overflow-hidden;
  padding-bottom: 140%; // Preserve aspect ratio
}

.placeholder-content {
  @apply flex-center size-full top-0 absolute;
}

.novel-title {
  @apply font-semibold line-clamp-2 lg:line-clamp-1;
}

.view-count {
  @apply flex gap-1 items-center text-gray-200 fill-slate-200;
}

.view-count-icon {
  @apply h-10 w-10 text-gray-200 dark:text-gray-600;
}

.text-footer {
  @apply p-1 cursor-pointer font-bold flex-row text-center justify-between items-center text-[0.75rem] bg-slate-400 dark:bg-neutral-600;
}

.text-footer:hover {
  @apply bg-red-500;
}

.footer-card-v1 {
  @apply w-full backdrop-blur-sm bg-gradient-to-t from-black absolute bottom-0 flex justify-start;
}

.container-card-v1 {
  @apply relative w-full h-0 overflow-hidden transition-transform duration-300 transform-gpu hover:-translate-y-2;
}

.novel-container {
  @apply shadow-md h-full flex flex-col rounded-t-lg rounded-b-md overflow-hidden relative;
}

.novel-link {
  @apply flex h-full w-full absolute;
  -webkit-mask-image: url("/card-1-mark.png");
  mask-image: url("/card-1-mark.png");

  /* Đặt kích thước mask vừa với phần đánh dấu */
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;

  /* Neo mask ở góc dưới cùng bên phải */
  -webkit-mask-position: bottom right;
  mask-position: bottom right;

  /* Không lặp lại mask */
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}
.novel-image {
  @apply object-cover size-full;
}

.novel-info {
  @apply text-sm p-1 w-full;
}

.novel-author-view {
  @apply flex justify-between w-full;
}

.view-count {
  @apply flex-start;
}

.view-count-text {
  @apply font-normal text-[0.7rem] text-center uppercase;
}

.chapter-title {
  @apply text-xs font-normal;
}

.chapter-update {
  @apply text-xs;
}

.text-footer {
  @apply text-sm;
}
.novel-button {
  @apply absolute z-50 right-0 bottom-0 h-[22%] w-[29%] text-white rounded-full flex-center bg-gradient-to-r from-red-400 via-red-500 to-red-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 shadow-lg shadow-red-500/50 dark:shadow-lg dark:shadow-red-800/80 font-medium text-sm text-center;
}
