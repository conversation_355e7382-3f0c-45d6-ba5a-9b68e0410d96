const fs = require("fs-extra");
const zlib = require("zlib");
const { create } = require("xmlbuilder2");
const path = require("path");

// Giả sử bạn có class DataSource tương đương trong Node.js
const { DataSource } = require("./dataSource"); // cần tự định nghĩa
require("dotenv").config();

const DOMAIN = process.env.DOMAIN;
const IP_ADDRESS = process.env.DB_HOST;
const PORT = process.env.DB_PORT;
const USER = process.env.DB_USER;
const PASSWORD = process.env.DB_PASSWORD;
const DATABASE = process.env.DB_NAME;

class SitemapGenerator {
  constructor(sitemapFile = "sitemap.xml", type = "sitemap") {
    this.sitemapFile = sitemapFile;
    this.type = type;

    if (type === "sitemap") {
      this.doc = create({ version: "1.0", encoding: "UTF-8" }).ele("urlset", {
        xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
      });
    } else if (type === "sitemapindex") {
      this.doc = create({ version: "1.0", encoding: "UTF-8" }).ele(
        "sitemapindex",
        { xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9" },
      );
    }
  }

  addUrl(
    loc,
    lastmod = null,
    priority = "0.5",
    changefreq = null,
    images = [],
  ) {
    const url = this.doc.ele("url");
    url.ele("loc").txt(loc);
    url.ele("lastmod").txt(lastmod || new Date().toISOString().split("T")[0]);
    if (changefreq) url.ele("changefreq").txt(changefreq);
    url.ele("priority").txt(priority);

    // Add image sitemap data if provided
    if (images && images.length > 0) {
      images.forEach((image) => {
        const imageElement = url.ele("image:image", {
          "xmlns:image": "http://www.google.com/schemas/sitemap-image/1.1",
        });
        imageElement.ele("image:loc").txt(image.url);
        if (image.caption) imageElement.ele("image:caption").txt(image.caption);
        if (image.title) imageElement.ele("image:title").txt(image.title);
      });
    }
  }

  addSitemap(loc, lastmod = null) {
    const sitemap = this.doc.ele("sitemap");
    sitemap.ele("loc").txt(loc);
    sitemap
      .ele("lastmod")
      .txt(lastmod || new Date().toISOString().split("T")[0]);
    console.log(`Added Sitemap: ${loc}`);
  }

  async save(folder = "", needGzip = false) {
    const xmlString = this.doc.end({ prettyPrint: true });
    const fullPath = path.join(folder, this.sitemapFile);

    await fs.outputFile(fullPath, xmlString, "utf-8");
    console.log(`Sitemap saved to ${fullPath}`);

    if (needGzip) {
      const gzPath = `${fullPath}.gz`;
      const fileContents = fs.createReadStream(fullPath);
      const writeStream = fs.createWriteStream(gzPath);
      const zip = zlib.createGzip();

      fileContents
        .pipe(zip)
        .pipe(writeStream)
        .on("finish", () => {
          fs.removeSync(fullPath);
          console.log(`Sitemap compressed to ${gzPath}`);
        });
    }
  }

  clearAll() {
    const rootName = this.type === "sitemap" ? "urlset" : "sitemapindex";
    this.doc = create({ version: "1.0", encoding: "UTF-8" }).ele(rootName, {
      xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
    });
    console.log("All URLs have been cleared from the sitemap.");
  }
}

async function generateSitemap(folder = "") {
  const dataSource = new DataSource(IP_ADDRESS, PORT, USER, PASSWORD, DATABASE);
  const allNovels = await dataSource.allNovels();
  const allGenres = await dataSource.allGenres();
  //   console.log("Total novels found: %d", allNovels.length);
  const novelDict = {};
  allNovels.forEach((novel) => {
    novelDict[novel.id] = novel.url;
  });

  const idxGenerator = new SitemapGenerator("sitemap.xml", "sitemapindex");
  idxGenerator.clearAll();
  idxGenerator.addSitemap(`${DOMAIN}/sitemap1.xml.gz`);

  const mainGenerator = new SitemapGenerator("sitemap1.xml");
  mainGenerator.clearAll();
  mainGenerator.addUrl(`${DOMAIN}/`, null, "1.0", "daily");
  mainGenerator.addUrl(`${DOMAIN}/tim-kiem`, null, "0.9", "weekly");
  mainGenerator.addUrl(`${DOMAIN}/ban-quyen`, null, "0.6");
  mainGenerator.addUrl(`${DOMAIN}/ve-chung-toi`, null, "0.6");
  mainGenerator.addUrl(`${DOMAIN}/dieu-khoan`, null, "0.6");

  for (const novel of allNovels) {
    mainGenerator.addUrl(
      `${DOMAIN}/truyen/${novel.url}-${novel.id}`,
      null,
      "0.8",
    );
  }
  for (const genre of allGenres) {
    mainGenerator.addUrl(`${DOMAIN}/the-loai/${genre.slug}`, null, "0.8");
  }

  await mainGenerator.save(folder, true);

  let i = 2;
  let offset = 0;
  const limit = 40000;

  while (true) {
    const chapters = await dataSource.allChapters(
      "id,novelid,url",
      offset,
      limit,
    );
    if (chapters.length === 0) break;

    const chapterGenerator = new SitemapGenerator(`sitemap${i}.xml`);
    for (const chapter of chapters) {
      const comicUrl = novelDict[chapter.novelid];
      const chapterId = chapter.id;
      const chapterUrl = chapter.url;
      chapterGenerator.addUrl(
        `${DOMAIN}/${comicUrl}/chuong-${chapterUrl}/${chapterId}`,
        null,
        "0.6",
        "daily",
      );
    }

    await chapterGenerator.save(folder, true);
    idxGenerator.addSitemap(`${DOMAIN}/sitemap${i}.xml.gz`);
    i += 1;
    offset += limit;
  }

  await idxGenerator.save(folder);
  await dataSource.close();
}

generateSitemap("sitemap/generated");
