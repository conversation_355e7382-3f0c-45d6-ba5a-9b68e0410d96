import { SettingType } from './SettingType.enum';

export enum InputType {
  Selection = 1,
  Text = 2,
  Number = 3,
  Range = 4,
  Color = 5,
  Toggle = 6,
  Slider = 7,
  None = 8,
}
export class SettingOption {
  inputType?: InputType;
  type: SettingType = SettingType.None;
  name?: string;
  description?: string;
  value?: any;
  group?: number;
  options?: any[] = [];
  min?: number;
  max?: number;
  step?: number;
}
