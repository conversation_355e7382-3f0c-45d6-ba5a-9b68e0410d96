.detail-container {
  @apply relative lg:container mx-auto;
}
.detail-bg-image {
  @apply absolute top-0 inset-0 h-80 bg-cover bg-center bg-no-repeat rounded-lg;
}
.detail-bg-image-overlay {
  @apply backdrop-blur-md bg-dark-background/30 dark:bg-dark-background/40 size-full;
}

.detail-novel-title {
  @apply text-xl md:text-3xl uppercase font-bold text-center md:text-left;
}

.detail-status-tag {
  @apply dark:bg-dark-background hover:bg-neutral-200 dark:hover:bg-dark-600 bg-neutral-100 rounded-lg p-1.5 flex flex-col items-center;
}

.card-detail-img {
  -webkit-mask-image: url("/card-mask.png");
  mask-image: url("/card-mask.png");

  -webkit-mask-size: auto 100%;
  mask-size: auto 100%;

  -webkit-mask-position: center center;
  mask-position: center center;

  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

.detail-genre-tag {
  @apply bg-neutral-100 dark:bg-dark-600  rounded-lg uppercase font-semibold inline-flex-center text-xs text-neutral-600 dark:text-gray-100 p-2 border border-gray-200 hover:border-primary-100 dark:border-dark-500;
}


