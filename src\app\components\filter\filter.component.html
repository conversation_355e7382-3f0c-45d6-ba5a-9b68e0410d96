<div
  class="p-6 pt-4 filter-section bg-neutral-100 dark:bg-neutral-800 rounded-lg shadow-common"
>
  <div class="flex-start mb-4">
    <svg
      fill="#000000"
      class="size-6"
      viewBox="-6 -6 36.00 36.00"
      id="filter"
      data-name="Flat Color"
      xmlns="http://www.w3.org/2000/svg"
      stroke="#000000"
      stroke-width="0.00024000000000000003"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0">
        <rect
          x="-6"
          y="-6"
          width="36.00"
          height="36.00"
          rx="18"
          fill="#000000"
          strokewidth="0"
        ></rect>
      </g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <path
          id="primary"
          d="M18,2H6A2,2,0,0,0,4,4V6.64a2,2,0,0,0,.46,1.28L9,13.36V21a1,1,0,0,0,.47.85A1,1,0,0,0,10,22a1,1,0,0,0,.45-.11l4-2A1,1,0,0,0,15,19V13.36l4.54-5.44A2,2,0,0,0,20,6.64V4A2,2,0,0,0,18,2Z"
          style="fill: #ffffff"
        ></path>
      </g>
    </svg>
    <h2 class="text-lg font-bold ml-2">BỘ LỌC</h2>
  </div>
  <div class="pt-4 border-t-[1px]"></div>
  <div *ngIf="!isGenrePage" class="mb-5">
    <div class="flex-start mb-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-keyboard"
      >
        <path d="M10 8h.01" />
        <path d="M12 12h.01" />
        <path d="M14 8h.01" />
        <path d="M16 12h.01" />
        <path d="M18 8h.01" />
        <path d="M6 8h.01" />
        <path d="M7 16h10" />
        <path d="M8 12h.01" />
        <rect width="20" height="16" x="2" y="4" rx="2" />
      </svg>
      <label class="block text-sm font-medium ml-2"> Tên truyện</label>
    </div>
    <input
      title="Search"
      name="keyword"
      type="search"
      (keydown.enter)="onSearch()"
      placeholder="Tìm tên truyện"
      [(ngModel)]="selectedValues.keyword"
      class="placeholder-current w-full text-base outline-1 dark:bg-dark-600 dark:outline-gray-700 outline-gray-300 rounded-lg focus:outline-1 cursor-pointer px-3 py-1 focus:outline-red-500 outline-none ng-valid ng-touched ng-dirty"
    />
  </div>
  <div *ngIf="!isGenrePage" class="mb-5">
    <div class="flex-start mb-2">
      <svg
        class="size-6"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        stroke="currentColor"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M8.5 3H11.5118C12.2455 3 12.6124 3 12.9577 3.08289C13.2638 3.15638 13.5564 3.27759 13.8249 3.44208C14.1276 3.6276 14.387 3.88703 14.9059 4.40589L20.5 10M7.5498 10.0498H7.5598M9.51178 6H8.3C6.61984 6 5.77976 6 5.13803 6.32698C4.57354 6.6146 4.1146 7.07354 3.82698 7.63803C3.5 8.27976 3.5 9.11984 3.5 10.8V12.0118C3.5 12.7455 3.5 13.1124 3.58289 13.4577C3.65638 13.7638 3.77759 14.0564 3.94208 14.3249C4.1276 14.6276 4.38703 14.887 4.90589 15.4059L8.10589 18.6059C9.29394 19.7939 9.88796 20.388 10.5729 20.6105C11.1755 20.8063 11.8245 20.8063 12.4271 20.6105C13.112 20.388 13.7061 19.7939 14.8941 18.6059L16.1059 17.3941C17.2939 16.2061 17.888 15.612 18.1105 14.9271C18.3063 14.3245 18.3063 13.6755 18.1105 13.0729C17.888 12.388 17.2939 11.7939 16.1059 10.6059L12.9059 7.40589C12.387 6.88703 12.1276 6.6276 11.8249 6.44208C11.5564 6.27759 11.2638 6.15638 10.9577 6.08289C10.6124 6 10.2455 6 9.51178 6ZM8.0498 10.0498C8.0498 10.3259 7.82595 10.5498 7.5498 10.5498C7.27366 10.5498 7.0498 10.3259 7.0498 10.0498C7.0498 9.77366 7.27366 9.5498 7.5498 9.5498C7.82595 9.5498 8.0498 9.77366 8.0498 10.0498Z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></path>
        </g>
      </svg>
      <label class="block text-sm font-medium ml-2">Thể loại</label>
    </div>
    <div class="relative">
      <button
        (click)="genresVisible = !genresVisible"
        #genresBtn3
        [ngClass]="{ 'border-primary-100': dataView.genres.length }"
        class="bg-white w-full dark:bg-dark-600 focus:border-primary-100 cursor-pointer flex-start gap-1 px-2 py-1 rounded-lg border border-gray-300 dark:border-neutral-700"
        aria-label="Thể loại"
      >
        <svg
          class="h-6 w-6 text-gray-600 dark:text-gray-500"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <line x1="12" y1="5" x2="12" y2="19" />
          <line x1="5" y1="12" x2="19" y2="12" />
        </svg>

        <div
          class="flex w-full flex-wrap gap-2 m-2"
          *ngIf="dataView.genres.length; else noGenres"
        >
          <div *ngFor="let genreKey of getGenreKeys()" class="genre-item">
            <div class="mask-container">
              <div class="mask-content">
                <span
                  class="text-sm"
                  [ngClass]="{
                    'line-through ': genreKey.select == 2,
                  }"
                  >{{ genreKey.label }}</span
                >
              </div>
            </div>

            <!-- Nút X để xóa -->
            <button
              (click)="removeGenre(genreKey.value)"
              class="text-primary-100 flex-start justify-end"
              aria-label="Xóa thể loại"
            >
              <svg
                class="h-4 w-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="10" />
                <line x1="15" y1="9" x2="9" y2="15" />
                <line x1="9" y1="9" x2="15" y2="15" />
              </svg>
            </button>
          </div>
        </div>

        <ng-template #noGenres>
          <div class="no-genre">Thêm thể loại</div>
        </ng-template>
      </button>
      <div
        app-genres
        (appClickOutside)="genresVisible = false"
        [elementIgnore]="genresBtn3"
        [disabled]="!genresVisible"
        class="absolute md:w-[32rem] lg:w-[42rem] flex top-[calc(100%+12px)] md:top-16 left-0 md:left-[calc(100%+30px)] md:-translate-y-1/2 w-full z-50"
        [(visible)]="genresVisible"
        (genreSelected)="onSelectGenre($event)"
        [statusGenres]="statusGenres"
      ></div>
    </div>
  </div>
  <div class="mb-5">
    <div class="flex-start mb-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-arrow-up-down"
      >
        <path d="m21 16-4 4-4-4"></path>
        <path d="M17 20V4"></path>
        <path d="m3 8 4-4 4 4"></path>
        <path d="M7 4v16"></path>
      </svg>
      <label class="block text-sm font-medium ml-2">Xắp xếp theo</label>
    </div>

    <div
      app-select
      [options]="dataView.sorts"
      [(selectedValue)]="selectedValues.sort"
      placeholder="Chọn cách sắp xếp"
      class="w-full dark:bg-dark-500"
    >
      <ng-template #selectedItem let-selectedOption class="dark:bg-dark-500">
        <div class="dark:bg-dark-500" *ngIf="selectedOption">
          <div class="text-sm dark:bg-current text-current">
            {{ selectedOption.name }}
          </div>
        </div>
      </ng-template>
      <ng-template #dropdownicon class="rounded-lg">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="#ffffff"
          width="16px"
          height="16px"
          viewBox="0 0 20 20"
        >
          <path d="M10 1L5 8h10l-5-7zm0 18l5-7H5l5 7z" />
        </svg>
      </ng-template>
    </div>
  </div>
  <div class="mb-5">
    <div class="flex-start mb-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-clock"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12 6 12 12 16 14"></polyline>
      </svg>
      <label class="block text-sm font-medium ml-2">Trạng thái</label>
    </div>

    <div
      app-select
      [options]="dataView.status"
      [(selectedValue)]="selectedValues.status"
      class="w-full"
    ></div>
  </div>
  <div class="mb-5">
    <div class="flex-start mb-2">
      <svg
        class="size-6"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M8 8H16M8 12H16M8 16H12M3.5 12C3.5 5.5 5.5 3.5 12 3.5C18.5 3.5 20.5 5.5 20.5 12C20.5 18.5 18.5 20.5 12 20.5C5.5 20.5 3.5 18.5 3.5 12Z"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></path>
        </g>
      </svg>
      <label class="block text-sm font-medium ml-2">Số chữ</label>
    </div>

    <div
      app-select
      [options]="dataView.wordcounts!"
      [(selectedValue)]="selectedValues.wordcount"
      placeholder="Chọn cách sắp xếp"
      class="w-full rounded-lg"
    >
      <ng-template #dropdownicon>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="#ffffff"
          width="16px"
          height="16px"
          viewBox="0 0 20 20"
        >
          <path d="M10 1L5 8h10l-5-7zm0 18l5-7H5l5 7z" />
        </svg>
      </ng-template>
    </div>
  </div>
  <div class="mb-6">
    <div class="flex-start mb-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-languages size-5"
      >
        <path d="m5 8 6 6"></path>
        <path d="m4 14 6-6 2-3"></path>
        <path d="M2 5h12"></path>
        <path d="M7 2h1"></path>
        <path d="m22 22-5-10-5 10"></path>
        <path d="M14 18h6"></path>
      </svg>
      <label class="block text-sm font-medium ml-2">Dịch thuật</label>
    </div>

    <div
      app-select
      [options]="dataView.translations!"
      [(selectedValue)]="selectedValues.translation"
      placeholder="Chọn cách sắp xếp"
      class="w-full"
    >
      <ng-template #dropdownicon>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="#ffffff"
          width="16px"
          height="16px"
          viewBox="0 0 20 20"
        >
          <path d="M10 1L5 8h10l-5-7zm0 18l5-7H5l5 7z" />
        </svg>
      </ng-template>
    </div>
  </div>
  <button
    type="button"
    (click)="onSearch()"
    class="w-full py-2 bg-red-500 text-white rounded-2xl flex-center"
    aria-label="Tìm kiếm"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 mr-2"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
    Tìm kiếm
  </button>
</div>
