import { Injectable } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  public tasks: string[] = [];
  private ignoredRoutes = ['/novel/topview'];

  constructor(private router: Router) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.ClearAll();
      }
    });
  }

  TaskAdd(task: string) {
    if (this.ignoredRoutes.findIndex((r) => task.includes(r)) !== -1) {
      return;
    }
    this.tasks.push(task);
  }
  ClearAll() {
    this.tasks = [];
  }

  TaskRemove(task: string) {
    this.tasks = this.tasks.filter((t: string) => t !== task);
  }

  hasTasks = () => this.tasks.length > 0;
}
