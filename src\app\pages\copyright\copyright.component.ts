import { Component } from '@angular/core';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';

@Component({
  selector: 'main[app-copyright]',
  imports: [ImgLayoutComponent],
  templateUrl: './copyright.component.html',
  styleUrl: './copyright.component.scss',
})
export class CopyrightComponent {
  currentYear = new Date().getFullYear();
  appName = globalConfig.APP_NAME;

  constructor(private seoService: SeoService) {
    this.setupSeo();
  }

  private setupSeo(): void {
    const title = `Ch<PERSON>h sách bản quyền`;
    const description = `Thông tin về chính sách bản quyền và quy định sử dụng nội dung tại ${this.appName}. Chúng tôi tôn trọng quyền tác gi<PERSON> và cam kết bảo vệ quyền lợi của người sáng tạo nội dung.`;
    const url = `${globalConfig.BASE_URL}/ban-quyen`;

    const seoData = {
      title,
      description,
      type: 'article' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `bản quyền ${this.appName}, chính sách bản quyền, quyền tác giả, quy định sử dụng, luật bản quyền, DMCA, quyền sở hữu trí tuệ`,
      canonical: url,
      publishedTime: new Date().toISOString(),
      modifiedTime: new Date().toISOString(),
      section: 'Chính sách',
      author: this.appName,
      noindex: false,
      nofollow: false,
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateCopyrightPageSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Chính sách bản quyền', url: '/ban-quyen' },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateCopyrightPageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: `Chính sách bản quyền - ${this.appName}`,
      description: `Chính sách bản quyền và quy định về quyền tác giả tại ${this.appName}`,
      url: `${globalConfig.BASE_URL}/ban-quyen`,
      inLanguage: 'vi-VN',
      isPartOf: {
        '@type': 'WebSite',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      about: {
        '@type': 'Thing',
        name: 'Chính sách bản quyền',
        description: 'Quy định về quyền tác giả và sử dụng nội dung',
      },
      publisher: {
        '@type': 'Organization',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      mainEntity: {
        '@type': 'Thing',
        name: 'Chính sách bản quyền',
        description:
          'Quy định về quyền tác giả và sử dụng nội dung tại ' + this.appName,
      },
      datePublished: new Date().toISOString(),
      dateModified: new Date().toISOString(),
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Trang chủ',
            item: globalConfig.BASE_URL,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Chính sách bản quyền',
            item: `${globalConfig.BASE_URL}/ban-quyen`,
          },
        ],
      },
    };
  }
}
