import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';

@Component({
  selector: 'main[app-bookcase]',
  templateUrl: './bookcase.component.html',
  styleUrl: './bookcase.component.scss',
  standalone: false,
})
export class BookcaseComponent {
  constructor(private router: Router) {}
  selectedTab = 0;
  onTabChange(tabid: number) {
    this.router.navigate([this.tabs[tabid].routerLink]);
    this.selectedTab = tabid;
  }
  refresh() {
    const currentUrl = this.router.url.split('/')[2]; // Get the current URL path after '/price/'
    const tabIndex = this.tabs.findIndex(
      (tab) => tab.routerLink.split('/')[2] === currentUrl,
    ); // Find the index of the tab that matches the current URL
    if (tabIndex !== -1) {
      this.selectedTab = tabIndex; // Set the selected tab index if found
    }
  }

  ngOnInit() {
    this.refresh();
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.refresh();
      }
    });
  }
  tabs = [
    {
      id: 0,
      name: '<PERSON>ang theo dõi',
      routerLink: '/tu-sach/theo-doi',
      icon: `<svg class="size-6"  fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
</svg>
`,
    },
    {
      id: 1,
      name: 'Đã tải xuống',
      routerLink: '/tu-sach/da-mua',
      icon: `<svg class="size-6"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />  <polyline points="7 10 12 15 17 10" />  <line x1="12" y1="15" x2="12" y2="3" /></svg>`,
    },
  ];
}
