<div
  app-img-layout
  [title]="'Giới thiệu về ' + appName"
  [description]="
    `N<PERSON>n tảng đọc truyện chữ hiện đạ<PERSON>, mư<PERSON><PERSON> mà và thân thiện – nơi bạn có thể
        đắm mình trong kho truyện phong phú, đ<PERSON><PERSON><PERSON> cập nhật liên tục mỗi ngày.`
  "
>
  <!-- Content -->
  <!-- Why -->
  <div class="mb-8">
    <h2
      class="inline-flex items-center gap-2 text-xl md:text-2xl font-semibold tracking-wide"
    >
      <svg
        class="size-6 text-amber-500"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path
          d="M12 2l2.09 6.26H20l-5 3.64L17.18 18 12 14.77 6.82 18 9 11.9 4 8.26h5.91L12 2z"
        />
      </svg>
      Tạ<PERSON> sao đ<PERSON><PERSON> tru<PERSON>n trên {{ appName }}?
    </h2>

    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      <!-- Card 1 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-emerald-100 text-emerald-600 dark:bg-emerald-400/10 dark:text-emerald-300 ring-1 ring-emerald-200/60 dark:ring-emerald-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M19 2H8a2 2 0 00-2 2v3H5a3 3 0 00-3 3v7a3 3 0 003 3h11a2 2 0 002-2v-3h1a3 3 0 003-3V5a3 3 0 00-3-3zm1 10a1 1 0 01-1 1h-1V9a2 2 0 00-2-2H8V4h11a1 1 0 011 1v7z"
              />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Thư viện đa dạng, kiểm duyệt kỹ</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Kho truyện phong phú nhiều thể loại, luôn được kiểm duyệt kỹ lưỡng
              và cập nhật liên tục.
            </p>
          </div>
        </div>
      </div>
      <!-- Card 2 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-sky-100 text-sky-600 dark:bg-sky-400/10 dark:text-sky-300 ring-1 ring-sky-200/60 dark:ring-sky-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M3 5a2 2 0 012-2h14a2 2 0 012 2v10a4 4 0 01-4 4h-2l-2 2-2-2H5a2 2 0 01-2-2V5z"
              />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Giao diện hiện đại, mượt mà</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Tối ưu cho đọc truyện chữ trên mọi thiết bị, hỗ trợ dark mode và
              font dễ đọc.
            </p>
          </div>
        </div>
      </div>
      <!-- Card 3 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-violet-100 text-violet-600 dark:bg-violet-400/10 dark:text-violet-300 ring-1 ring-violet-200/60 dark:ring-violet-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 7h10v2H7V7zm0 4h7v2H7v-2zm0 4h10v2H7v-2z" />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Tự động lưu chương đã đọc</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Theo dõi tiến độ, đánh dấu chương, lưu bookmark từ khoá, tác giả…
              giúp tìm lại nhanh chóng.
            </p>
          </div>
        </div>
      </div>
      <!-- Card 4 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-amber-100 text-amber-700 dark:bg-amber-400/10 dark:text-amber-300 ring-1 ring-amber-200/60 dark:ring-amber-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 3l4 4H8l4-4zm-6 6h12v12H6z" />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Truy cập nhanh tủ truyện</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Xem nhanh truyện đang theo dõi, đã đọc và đã lưu ngay từ trang
              chủ.
            </p>
          </div>
        </div>
      </div>
      <!-- Card 5 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-rose-100 text-rose-600 dark:bg-rose-400/10 dark:text-rose-300 ring-1 ring-rose-200/60 dark:ring-rose-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 3a9 9 0 100 18 9 9 0 000-18z" />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Audio đọc truyện & tải truyện</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Nghe truyện mọi lúc mọi nơi, hỗ trợ tải xuống để đọc offline tiện
              lợi.
            </p>
          </div>
        </div>
      </div>
      <!-- Card 6 -->
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm hover:shadow transition-shadow"
      >
        <div class="flex items-start gap-3">
          <div
            class="size-10 flex-center rounded-lg bg-cyan-100 text-cyan-600 dark:bg-cyan-400/10 dark:text-cyan-300 ring-1 ring-cyan-200/60 dark:ring-cyan-300/20"
          >
            <svg class="size-5" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 7l5 5-5 5-1.41-1.41L13.17 13H6v-2h7.17l-2.58-2.59L12 7z"
              />
            </svg>
          </div>
          <div>
            <h3 class="font-semibold">Đồng bộ nhiều thiết bị</h3>
            <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
              Chuyển đổi dữ liệu mượt mà giữa nhiều thiết bị hoặc trình duyệt.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Payment -->
  <div class="mb-8">
    <h2
      class="inline-flex items-center gap-2 text-xl md:text-2xl font-semibold tracking-wide"
    >
      <svg
        class="size-6 text-emerald-500"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M3 7h18v10a2 2 0 01-2 2H5a2 2 0 01-2-2V7zm18-2H3V3h18v2z" />
      </svg>
      Tính năng thanh toán & giao dịch
    </h2>
    <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm"
      >
        <h3 class="font-semibold">Nạp tiền tự động</h3>
        <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
          Hoàn tất chỉ sau vài giây, trải nghiệm liền mạch.
        </p>
      </div>
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm"
      >
        <h3 class="font-semibold">Minh bạch, rõ ràng</h3>
        <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
          Lưu trữ đầy đủ lịch sử giao dịch, dễ theo dõi.
        </p>
      </div>
      <div
        class="rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-4 shadow-sm"
      >
        <h3 class="font-semibold">Phân loại thông minh</h3>
        <p class="mt-1 text-sm text-neutral-600 dark:text-dark-100">
          Quản lý giao dịch theo loại, kiểm soát thuận tiện.
        </p>
      </div>
    </div>
  </div>

  <!-- Contact -->
  <div class="mb-10">
    <h2
      class="inline-flex items-center gap-2 text-xl md:text-2xl font-semibold tracking-wide"
    >
      <svg class="size-6 text-rose-500" viewBox="0 0 24 24" fill="currentColor">
        <path
          d="M21 8V7l-3 2-2-1-2 1-2-1-2 1-2-1-3 2v1l3-2 2 1 2-1 2 1 2-1 2 1 3-2zM3 9v8a2 2 0 002 2h14a2 2 0 002-2V9l-3 2-2-1-2 1-2-1-2 1-2-1-3-2z"
        />
      </svg>
      Liên hệ
    </h2>
    <div
      class="mt-4 rounded-2xl bg-neutral-50 dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 p-5 md:p-6 shadow-sm"
    >
      <p>
        Nếu bạn có góp ý, báo lỗi hoặc đề xuất hợp tác, hãy liên hệ qua email:
        <a
          href="mailto:<EMAIL>"
          class="text-primary-600 dark:text-primary-300 font-medium underline underline-offset-4 decoration-2 hover:text-primary-700"
          >saytruyenhot&#64;gmail.com</a
        >.
      </p>
      <p class="mt-2 text-sm text-neutral-600 dark:text-dark-100">
        Với các yêu cầu quảng cáo, vui lòng ghi rõ chủ đề email là
        <strong>[Quảng cáo]</strong> để được hỗ trợ nhanh hơn.
      </p>
    </div>
  </div>

  <!-- Closing -->
  <div class="mb-14">
    <div class="mx-auto max-w-2xl text-center">
      <figure
        class="relative px-6 py-6 md:py-8 rounded-xl bg-white dark:bg-dark-700 ring-1 ring-neutral-200 dark:ring-dark-500/70 shadow-sm"
      >
        <blockquote class="text-base md:text-lg leading-relaxed">
          <strong>{{ appName }}</strong> — Mỗi trang truyện chữ là một hành
          trình.
        </blockquote>
      </figure>
    </div>
  </div>
</div>
