.genre-container {
  @apply flex flex-col bg-white  dark:bg-neutral-800 w-full h-[32rem] overflow-y-auto z-50  shadow-2xl shadow-black/50 space-y-2 rounded-lg text-sm px-6 py-4 scrollbar-style-sm;
}

.genre-title {
  @apply text-xs flex capitalize;
}

.genre-label {
  @apply ml-2 text-xs not-italic;
}

.genre-search {
  @apply flex-start relative mb-2;
}

.genre-search-icon {
  @apply absolute left-2 p-2 rounded-lg;
}

.genre-input {
  @apply bg-gray-100 dark:bg-neutral-700 text-sm w-full h-8 rounded-lg pl-10 pr-4 focus:border focus:border-primary-100 dark:focus:bg-neutral-600 outline-none focus:bg-white;
}

.genre-list {
  @apply space-y-1;
}

.genre-list-header {
  @apply flex gap-2 items-center;
}

.genre-list-title {
  @apply text-lg select-none font-normal;
}

.genre-list-divider {
  @apply border my-4  flex-grow dark:border-neutral-700;
}

.genre-list-items {
  @apply flex gap-2 flex-wrap;
}

.genre-list-item {
  @apply cursor-pointer bg-gray-200 rounded-full flex-start h-9 shadow-lg dark:bg-dark-550 hover:text-red-500;
}

.genre-list-item-normal {
  @apply outline-1 outline-gray-300  dark:outline-gray-600 dark:bg-neutral-900 outline-dashed hover:outline-primary-100 hover:text-primary-100;
}

.genre-list-item-active {
  @apply border border-dashed  border-red-500 text-primary-100;
}

.genre-list-item-disabled {
  @apply text-red-500 bg-red-200 line-through;
}

.genre-list-item-link {
  @apply px-1 my-auto text-center;
}

.genre-list-item-name {
  @apply my-auto select-none text-xs font-medium relative bottom-[1px];
}

.genre-hover-info {
  @apply space-y-1 transition-all ease-in-out duration-700;
}

.genre-hover-divider {
  @apply border my-2  dark:border-neutral-700;
}

.genre-hover-content {
  @apply flex gap-1;
}

.genre-hover-icon {
  @apply capitalize text-lg select-none;
}

.genre-hover-desc {
  @apply text-[0.75rem] font-medium text-gray-700 dark:text-gray-300 text-start;
}
