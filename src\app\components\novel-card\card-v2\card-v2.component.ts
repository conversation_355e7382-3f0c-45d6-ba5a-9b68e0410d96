import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { AuthorIconComponent } from '@components/icons/author/author.component';
import { Novel } from '@schemas/Novel';
import { PinesModule } from 'src/app/shared/pines/pines.module';

@Component({
  selector: '[app-card-v2]',
  templateUrl: './card-v2.component.html',
  styleUrl: './card-v2.component.scss',
  imports: [CommonModule, RouterLink, PinesModule, AuthorIconComponent],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'cardv2-panel',
    '[class]': 'size',
  },

})
export class NovelCardV2Component {
  @Input() size: string = 'cardv2-small md:cardv2-medium';
  @Input() novel?: Novel;
  constructor() {}
  ngOnInit() {}
  ngAfterViewInit() {}
}
