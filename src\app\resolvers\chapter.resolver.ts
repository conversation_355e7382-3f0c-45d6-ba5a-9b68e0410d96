import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { ChapterList } from '@schemas/ChapterList';
import { ChapterPage } from '@schemas/ChapterPage';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { catchError, map, Observable, of } from 'rxjs';

export const chapterResolver: ResolveFn<
  Observable<IServiceResponse<ChapterPage> | null>
> = (route) => {
  let id = route.paramMap.get('chapter-id');
  if (!Number.isInteger(Number(id))) return of(null);
  return inject(NovelService)
    .getChapterPage(Number(id))
    .pipe(
      map((res) => {
        return res;
      }),
      catchError((err) => {
        return of(null);
      }),
    );
};

export const listChapterResolver: ResolveFn<
  Observable<IServiceResponse<ChapterList> | null>
> = (route) => {
  let id = route.paramMap.get('slug');
  id = id?.split('-').pop() || '';
  if (!Number.isInteger(Number(id))) return of(null);
  return inject(NovelService)
    .getChapters(Number(id), 1, 200)
    .pipe(
      map((res) => {
        return res;
      }),
      catchError((err) => {
        return of(null);
      }),
    );
};
