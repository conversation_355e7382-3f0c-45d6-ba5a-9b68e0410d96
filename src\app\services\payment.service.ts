import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Item } from '@schemas/Item';
import { IServiceResponse } from '@schemas/ResponseType';
import { UrlService } from './url.service';
// import { INotification, IServiceResponse, User, VoteInfo } from '@schema';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  constructor(
    private httpClient: HttpClient,
    private urlService: UrlService,
  ) {}

  getBuyItems() {
    let req = this.httpClient.get<IServiceResponse<Item[]>>(
      `${this.urlService.API_URL}/price/items`,
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
    return req;
  }

  buyItem(item: Item) {
    let req = this.httpClient.post<IServiceResponse<any>>(
      `${this.urlService.API_URL}/buy`,
      { itemId: item.id, amount: 1 },
      {
        transferCache: true,
        params: { expiration: 300 },
      },
    );
    return req;
  }
}
