<div class="profile-info-container">
  <!-- User Avatar Section -->
  <div class="avatar-section">
    <div class="avatar-wrapper">
      <img
        [src]="user.avatar || defaultAvatar"
        [alt]="user.firstName + ' ' + user.lastName"
        class="avatar-image"
        loading="lazy"
      />
      <div class="avatar-overlay">
        <label for="avatar-upload" class="avatar-upload-label">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
              clip-rule="evenodd"
            ></path>
          </svg>
          Đ<PERSON>i ảnh
        </label>
        <input
          id="avatar-upload"
          type="file"
          accept="image/*"
          (change)="onFileChange($event)"
          class="hidden"
        />
      </div>
    </div>
  </div>

  <!-- User Information -->
  <div class="user-info-section">
    <div class="user-basic-info">
      <h2 class="user-name">{{ user.firstName }} {{ user.lastName }}</h2>
      <p class="user-email">{{ user.email }}</p>

      <!-- VIP Status -->
      <div class="vip-status" [class.vip-active]="user.vip">
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5 16L3 5h5.5l1.5 5 1.5-5H16l-2 11h-4l-1.5-5L7 16H5z" />
        </svg>
        <span *ngIf="user.vip; else freeVersion">
          VIP - HSD: {{ transform(user.vipExpireAat) }}
        </span>
        <ng-template #freeVersion>
          <span>Thành viên miễn phí</span>
        </ng-template>
      </div>
    </div>

    <!-- User Details Grid -->
    <div class="user-details-grid">
      <div class="detail-item">
        <span class="detail-label">Họ tên</span>
        <span class="detail-value"
          >{{ user.firstName }} {{ user.lastName }}</span
        >
      </div>

      <div class="detail-item">
        <span class="detail-label">Email</span>
        <span class="detail-value">{{ user.email }}</span>
      </div>

      <div class="detail-item" *ngIf="user.dob">
        <span class="detail-label">Ngày sinh</span>
        <span class="detail-value">{{ transform(user.dob) }}</span>
      </div>

      <div class="detail-item" *ngIf="user.maxim">
        <span class="detail-label">Châm ngôn</span>
        <span class="detail-value">{{ user.maxim }}</span>
      </div>

      <div class="detail-item">
        <span class="detail-label">Ngày tham gia</span>
        <span class="detail-value">{{ transform(user.createAt) }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button class="btn btn-primary" (click)="toggleUpdateForm()">
        <svg
          class="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          ></path>
        </svg>
        Cập nhật thông tin
      </button>
    </div>
    <div app-dialog [(isVisible)]="showUpdateForm">
      <div
        app-update-info-form
        (onClose)="showUpdateForm = false"
        (onUpdate)="showUpdateForm = false"
      ></div>
    </div>
    <!-- Update Form -->
  </div>
</div>
