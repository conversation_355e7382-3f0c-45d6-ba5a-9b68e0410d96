import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ViewEncapsulation,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { IServiceResponse } from '@schemas/ResponseType';
import { EventService } from '@services/event.service';
import { NovelService } from '@services/novel.service';
import { DateAgoPipe } from 'src/app/shared/pines/date-ago.pine';

@Component({
  selector: '[app-update-panel]',
  standalone: true,
  imports: [CommonModule, RouterLink, DateAgoPipe],
  templateUrl: './update-panel.component.html',
  styleUrl: './update-panel.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UpdatePanelComponent {
  constructor(
    private novelService: NovelService,
    private eventService: EventService,
    private cd: ChangeDetectorRef,
  ) {}
  lastestNovels: Novel[] = [];
  ngOnInit() {
    this.novelService
      .getNovels({
        page: '1',
        step: '20',
        status: '-1',
        sort: '1',
        genre: '-1',
      })
      .subscribe((res: IServiceResponse<NovelList>) => {
        if (res.data) this.lastestNovels = res.data.novels;
        this.cd.detectChanges();
      });
  }
  onHoverNovel(novel?: Novel) {
    this.eventService.OnShowComicDetail.next(novel);
  }

  ngOnDestroy() {
    this.eventService.OnShowComicDetail.next(undefined);
  }
}
