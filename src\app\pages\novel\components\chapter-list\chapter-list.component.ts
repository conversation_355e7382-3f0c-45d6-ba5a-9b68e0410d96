import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { SelectComponent } from '@components/select/select.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { IOption } from '@schemas/IOption';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: '[app-chapter-list]',
  standalone: true,
  templateUrl: './chapter-list.component.html',
  styleUrl: './chapter-list.component.scss',
  imports: [CommonModule, RouterLink, SelectComponent, SpinnerComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterListComponent implements OnInit, OnDestroy {
  @Input()
  novel?: Novel;

  @ViewChild('searchChapterInput')
  searchChapterInput!: ElementRef<HTMLInputElement>;
  @ViewChild('overlay')
  overlayEl!: ElementRef<HTMLElement>;
  allchapters: Chapter[] = [];
  history: number[] = [];

  options: IOption[] = [
    {
      label: '0 - 100',
      value: 1,
    },
  ];
  isChapterLoading = false;

  totalChapterPage = 1;
  currentPage = 1;
  sizePage = 200;

  isIncrease = true;
  private destroy$ = new Subject<void>();
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object,
    private novelService: NovelService,
  ) {}
  ngOnInit() {
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (!this.novel) return;
      this.totalChapterPage = Math.ceil(this.novel!.numChapter / this.sizePage);

      const page = params.get('page')?.replace('trang-', '') || 1;
      if (!Number.isInteger(Number(page))) {
        this.router.navigate(['/not-found']);
        return;
      }
      this.currentPage = Number(page);
      if (this.currentPage > this.totalChapterPage) {
        this.router.navigate(['/not-found']);
        return;
      }
      this.fetchChapters();
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  fetchChapters() {
    // if(isPlatformServer(this.platformId)) return

    this.isChapterLoading = true;
    this.novelService
      .getChapters(this.novel?.id!, this.currentPage, this.sizePage)
      .pipe(takeUntil(this.destroy$))
      .subscribe((res: IServiceResponse<ChapterList>) => {
        this.isChapterLoading = false;
        if (!res.data) return;
        this.allchapters = [...res.data.chapters];
        if (!this.isIncrease) {
          this.allchapters.reverse();
        }
        this.totalChapterPage = Math.ceil(
          this.novel!.numChapter / this.sizePage,
        );
        this.sizePage = res.data.step;
        const sign = this.isIncrease ? 1 : -1;
        const length = this.isIncrease ? 0 : this.totalChapterPage - 1;
        this.options = Array.from({ length: this.totalChapterPage }, (_, i) => {
          return {
            label: `${(length + i * sign) * this.sizePage} - ${(length + i * sign + 1) * this.sizePage}`,
            value: length + i * sign + 1,
          };
        });
        this.cd.detectChanges();
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['novel'] && !changes['novel'].firstChange) {
      this.fetchChapters();
    }
  }
  ngAfterViewInit() {}

  onPageChange(value: number) {
    this.goToPage(value);
  }
  onOrderChange($event: Event) {
    this.isIncrease = ($event.target as HTMLInputElement).checked;
    let page = this.isIncrease ? 1 : this.totalChapterPage;
    this.goToPage(page);
  }

  goToPage(page: number) {
    if (page == this.currentPage) return;
    this.router.navigate(
      ['/truyen', this.novel?.url + '-' + this.novel?.id, 'trang-' + page],
      {
        state: { canScroll: false },
      },
    );
  }
}
