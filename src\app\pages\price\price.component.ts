import { Component } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';

@Component({
  selector: 'main[app-price]',
  standalone: false,
  templateUrl: './price.component.html',
  styleUrl: './price.component.scss',
})
export class PriceComponent {
  selectedTab = 0;
  appName = globalConfig.APP_NAME;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private seoService: SeoService,
  ) {
    this.setupSeo();
  }

  onTabChange(tabid: number) {
    this.router.navigate([this.tabs[tabid].routerLink]);
    this.selectedTab = tabid;
  }

  Refresh() {
    const currentUrl = this.router.url.split('/')[2]; // Get the current URL path after '/price/'
    console.log(currentUrl);

    const tabIndex = this.tabs.findIndex(
      (tab) => tab.routerLink.split('/')[2] === currentUrl,
    ); // Find the index of the tab that matches the current URL
    if (tabIndex !== -1) {
      this.selectedTab = tabIndex; // Set the selected tab index if found
    }
  }

  ngOnInit() {
    this.Refresh();
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.Refresh();
      }
    });
  }

  private setupSeo(): void {
    const title = `Bảng giá dịch vụ`;
    const description = `Xem bảng giá các gói dịch vụ VIP và xu tại ${this.appName}. Nâng cấp tài khoản để trải nghiệm đọc truyện chữ premium với nhiều tính năng độc quyền và không quảng cáo.`;
    const url = `${globalConfig.BASE_URL}/price`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `bảng giá ${this.appName}, nạp VIP, mua xu, gói dịch vụ premium, nâng cấp tài khoản, thanh toán online, đọc truyện VIP`,
      canonical: url,
      section: 'Dịch vụ',
      noindex: false,
      nofollow: false,
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generatePricePageSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Bảng giá', url: '/price' },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generatePricePageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: `Dịch vụ Premium ${this.appName}`,
      description: `Các gói dịch vụ VIP và xu tại ${this.appName} giúp nâng cao trải nghiệm đọc truyện`,
      url: `${globalConfig.BASE_URL}/price`,
      provider: {
        '@type': 'Organization',
        name: this.appName,
        url: globalConfig.BASE_URL,
      },
      serviceType: 'Premium Reading Service',
      areaServed: 'VN',
      offers: [
        {
          '@type': 'Offer',
          name: 'Gói VIP',
          description:
            'Nâng cấp tài khoản VIP để trải nghiệm đọc truyện premium',
          url: `${globalConfig.BASE_URL}/price/vip`,
        },
        {
          '@type': 'Offer',
          name: 'Nạp xu',
          description: 'Nạp xu để mở khóa các chương truyện premium',
          url: `${globalConfig.BASE_URL}/price/coin`,
        },
      ],
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Trang chủ',
            item: globalConfig.BASE_URL,
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Bảng giá',
            item: `${globalConfig.BASE_URL}/price`,
          },
        ],
      },
    };
  }

  tabs = [
    {
      id: 0,
      name: 'Nạp VIP',
      routerLink: '/price/vip',
      icon: `<svg
      class="size-6"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <g>
          <path fill="none" d="M0 0h24v24H0z"></path>
          <path
            fill-rule="nonzero"
            d="M3.492 8.065L4.778 19h14.444l1.286-10.935-4.01 2.673L12 4.441l-4.498 6.297-4.01-2.673zM2.801 5.2L7 8l4.186-5.86a1 1 0 0 1 1.628 0L17 8l4.2-2.8a1 1 0 0 1 1.547.95l-1.643 13.967a1 1 0 0 1-.993.883H3.889a1 1 0 0 1-.993-.883L1.253 6.149A1 1 0 0 1 2.8 5.2zM12 15a2 2 0 1 1 0-4 2 2 0 0 1 0 4z"
          ></path>
        </g>
      </g>
    </svg>`,
    },
    {
      id: 1,
      name: 'Nạp Xu',
      routerLink: '/price/coin',
      icon: `<svg class="size-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
                <path
                    d="M12 16H13C13.6667 16 15 15.6 15 14C15 12.4 13.6667 12 13 12H11C10.3333 12 9 11.6 9 10C9 8.4 10.3333 8 11 8H12M12 16H9M12 16V18M15 8H12M12 8V6M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </g>
        </svg>`,
    },
  ];
}
