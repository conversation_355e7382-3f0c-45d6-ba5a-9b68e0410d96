import { Injectable } from '@angular/core';
import { FeatureFlags } from '@schemas/enum';

@Injectable({
  providedIn: 'root',
})
export class FeatureFlagsService {
  private readonly flags: Record<FeatureFlags, boolean> = {
    [FeatureFlags.FEATURE_COIN]: false,
    [FeatureFlags.FEATURE_VIP]: false,
    [FeatureFlags.FEATURE_DOWNLOAD_NOVEL]: false,
  };

  isEnabled(flag: FeatureFlags): boolean {
    return !!this.flags[flag];
  }

  getAllFlags(): Record<string, boolean> {
    return this.flags;
  }
}
