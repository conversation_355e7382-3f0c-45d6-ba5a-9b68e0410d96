.panel-mark {
  height: 100%; /* <PERSON><PERSON><PERSON><PERSON> chỉnh tùy theo nhu cầu */
  // background: url('your-image.jpg') no-repeat center/cover;

  /* <PERSON><PERSON> dụng ảnh làm mask */
  -webkit-mask-image: url("/panel-mark.png");
  mask-image: url("/panel-mark.png");

  /* <PERSON>i<PERSON><PERSON> chỉnh kích thước mask */
  // -webkit-mask-size: auto ; /* Giữ chiều cao 100%, chiều rộng tự động */
  mask-size: auto auto;
  /* Căn góc trên bên phải */
  -webkit-mask-position: left top;
  mask-position: left top;

  // -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  //cover
  @apply bg-white dark:bg-dark-700 w-full absolute top-0 pt-16 px-6 pb-6 h-full -z-0;
}
.panel-header {
  @apply flex-center mt-2 px-2 gap-2;
}
.panel-title {
  @apply w-full text-xl font-semibold;
}
.panel-container {
  @apply bg-neutral-200 dark:bg-dark-600 h-full w-full shadow-common rounded-2xl overflow-hidden relative flex flex-col;
}
