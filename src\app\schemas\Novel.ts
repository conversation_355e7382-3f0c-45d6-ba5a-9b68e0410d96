import { Chapter } from './Chapter';
import { Genre } from './Genre';

export interface Novel {
  id: number;
  title: string;
  otherName?: string;
  url: string;
  author?: string;
  description?: string | null;
  coverImage?: string;
  viewCount: number;
  status: number;
  rating: number;
  // createAt?: string;
  updateAt: string;
  genres: Genre[];
  chapters: Chapter[];
  isFollow?: boolean;
  numChapter: 0;
  type: false;
  wordCount: number;
  translation: number;
}
