import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
  OnInit,
  OnChanges,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { AuthorIconComponent } from '@components/icons/author/author.component';
import { PanelComponent } from '@components/panel/panel.component';
import { Novel } from '@schemas/Novel';
import { NumeralPipe } from 'src/app/shared/pines/numeral.pipe';

@Component({
  selector: '[app-top-novel]',
  imports: [
    CommonModule,
    PanelComponent,
    RouterLink,
    AuthorIconComponent,
    NumeralPipe,
  ],
  templateUrl: './top-novel.component.html',
  styleUrl: './top-novel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class TopNovelComponent implements OnInit, OnChanges {
  @Input() title: string = '';
  @Input() novels: Novel[] = [];

  // Pre-computed arrays for better performance
  protected novelIndices: number[] = [];
  protected hasValidNovels: boolean = false;

  ngOnInit() {
    this.precomputeData();
  }

  ngOnChanges() {
    this.precomputeData();
  }

  private precomputeData() {
    this.hasValidNovels = this.novels && this.novels.length > 0;
    this.novelIndices = this.hasValidNovels ? [1, 2, 3, 4, 5, 6] : [];
  }

  // Optimized trackBy function
  trackByIndex(index: number): number {
    return index;
  }

  // Method to get novel safely
  getNovel(index: number): Novel | null {
    return this.novels && this.novels[index] ? this.novels[index] : null;
  }
}
