import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { catchError, map, Observable, of } from 'rxjs';

export const recommendResolver: ResolveFn<
  Observable<IServiceResponse<Novel[]> | null>
> = (route) => {
  const novelService = inject(NovelService);
  return novelService.getRecommendNovels().pipe(
    map((res: IServiceResponse<Novel[]>) => {
      return res.status == 0 ? null : res;
    }),
    catchError(() => of(null)),
  );
};
