<div app-panel title="ĐÃ ĐỌC">
  <ng-template #iconTemplate>
    <svg
      class="size-7"
      viewBox="-1.2 -1.2 26.40 26.40"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12ZM11.8284 6.75736C12.3807 6.75736 12.8284 7.20507 12.8284 7.75736V12.7245L16.3553 14.0653C16.8716 14.2615 17.131 14.8391 16.9347 15.3553C16.7385 15.8716 16.1609 16.131 15.6447 15.9347L11.4731 14.349C11.085 14.2014 10.8284 13.8294 10.8284 13.4142V7.75736C10.8284 7.20507 11.2761 6.75736 11.8284 6.75736Z"
          fill="currentColor"
        ></path>
      </g>
    </svg>
  </ng-template>

  <div class="flex flex-col gap-2 w-full sm:h-60">
    <ng-container *ngIf="novels.length == 0; else novelList">
      <div class="size-full flex-center">
        <div app-empty></div>
      </div>
    </ng-container>
    <ng-template #novelList>
      <ng-container *ngFor="let novel of novels | slice: 0 : 5; index as i">
        <a
          [routerLink]="['/truyen', novel.url + '-' + novel.id]"
          class="flex flex-col w-full"
        >
          <div class="line-clamp-2 font-medium text-sm flex gap-1">
            <svg
              class="flex-shrink-0 size-4"
              focusable="false"
              viewBox="0 0 24 24"
              data-testid="MenuBookIcon"
              fill="currentColor"
            >
              <path
                d="M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z"
              ></path>
              <path
                d="M17.5 10.5c.88 0 1.73.09 2.5.26V9.24c-.79-.15-1.64-.24-2.5-.24-1.7 0-3.24.29-4.5.83v1.66c1.13-.64 2.7-.99 4.5-.99zM13 12.49v1.66c1.13-.64 2.7-.99 4.5-.99.88 0 1.73.09 2.5.26V11.9c-.79-.15-1.64-.24-2.5-.24-1.7 0-3.24.3-4.5.83zm4.5 1.84c-1.7 0-3.24.29-4.5.83v1.66c1.13-.64 2.7-.99 4.5-.99.88 0 1.73.09 2.5.26v-1.52c-.79-.16-1.64-.24-2.5-.24z"
              ></path>
            </svg>
            <span class="line-clamp-1">
              {{ novel.title }}
            </span>
          </div>
          <div class="ml-5 flex justify-between">
            <span class="line-clamp-1 font-extralight text-xs"
              >Chương {{ novel.chapters[0].slug }}</span
            >
            <span class="text-primary-100 font-extralight text-xs">
              Đọc tiếp</span
            >
          </div>
        </a>
      </ng-container>
    </ng-template>
  </div>
</div>
