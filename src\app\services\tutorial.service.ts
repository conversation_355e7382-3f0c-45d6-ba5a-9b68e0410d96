// tutorial.service.ts
import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Subject } from 'rxjs';

export enum TutorialStatus {
  Start,
  Done,
  End,
}
@Injectable({ providedIn: 'root' })
export class TutorialService {
  handers: Map<string, Subject<TutorialStatus>> = new Map<
    string,
    Subject<TutorialStatus>
  >();

  private toturials: Map<string, Tutorial> = new Map<string, Tutorial>();
  private currentStepIndex = 0;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      // timer(1000).subscribe(() => this.Excute('chapter'));
    }
  }

  public setToturial(group: string, step: TutorialStep) {
    if (this.toturials.has(group)) {
      this.toturials.get(group)?.steps.push(step);
      this.toturials.get(group)?.steps.sort((a, b) => a.order - b.order);
    } else {
      this.toturials.set(group, {
        steps: [step],
        idx: 0,
        title: '',
        content: '',
      });
    }
    this.Register(group + step.order);
  }
  Register(group: string) {
    this.handers.set(group, new Subject());
  }

  public On(group: string, order: number) {
    return this.handers.get(group + order);
  }

  public Next(group: string) {
    let tutorial = this.toturials.get(group);
    console.log('tutorial', tutorial);

    if (tutorial) {
      this.handers
        .get(group + tutorial.steps[tutorial.idx].order)
        ?.next(TutorialStatus.Done);
      tutorial.idx += 1;
      this.handers
        .get(group + tutorial.steps[tutorial.idx].order)
        ?.next(TutorialStatus.Start);
    }
  }

  public Back(group: string) {
    let tutorial = this.toturials.get(group);
    if (tutorial) {
      this.handers
        .get(group + tutorial.steps[tutorial.idx].order)
        ?.next(TutorialStatus.Done);
      tutorial.idx -= 1;
      this.handers
        .get(group + tutorial.steps[tutorial.idx].order)
        ?.next(TutorialStatus.Start);
    }
  }

  public Excute(group: string) {
    let tutorial = this.toturials.get(group);
    if (tutorial) {
      this.handers
        .get(group + tutorial.steps[tutorial.idx].order)
        ?.next(TutorialStatus.Start);
    }
  }
  // setSteps(steps: TutorialStep[]) {
  //     this.steps = steps;
  //     this.currentStepIndex = 0;
  // }

  // getCurrentStep(): TutorialStep | null {
  //     return this.steps[this.currentStepIndex] ?? null;
  // }

  // nextStep() {
  //     if (this.currentStepIndex < this.steps.length - 1) {
  //         this.currentStepIndex++;
  //     }
  // }

  // prevStep() {
  //     if (this.currentStepIndex > 0) {
  //         this.currentStepIndex--;
  //     }
  // }

  // isLastStep(): boolean {
  //     return this.currentStepIndex === this.steps.length - 1;
  // }

  // endTutorial() {
  //     localStorage.setItem('tutorial_shown', 'true');
  //     this.steps = [];
  // }

  // hasSeenTutorial(): boolean {
  //     return localStorage.getItem('tutorial_shown') === 'true';
  // }
}

export interface Tutorial {
  steps: TutorialStep[];
  idx: number;
  title: string;
  content: string;
}

export interface TutorialStep {
  order: number;
  title: string;
  content: string;
}
