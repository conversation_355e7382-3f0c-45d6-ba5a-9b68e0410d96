import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NovelService } from '../services/novel.service';
import { SeoService } from '../services/seo.service';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';
import { IServiceResponse } from '@schemas/ResponseType';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: '[app-author]',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './author.component.html',
  styleUrls: ['./author.component.scss'],
})
export class AuthorComponent implements OnInit {
  authorSlug: string = '';
  authorName: string = '';
  novels: Novel[] = [];
  isLoading = true;
  errorMessage: string = '';

  // Statistics
  totalNovels = 0;
  totalViews = 0;
  totalFollowers = 0;
  avgRating = 0;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private novelService: NovelService,
    private seoService: SeoService,
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.authorSlug = params['authorSlug'];
      this.authorName = this.convertSlugToName(this.authorSlug);
      this.setupSEO();
      this.loadAuthorNovels();
    });
  }

  private convertSlugToName(slug: string): string {
    return slug
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  private setupSEO(): void {
    this.seoService.setSEOData({
      title: `Tác giả ${this.authorName} - SayTruyenHot`,
      description: `Danh sách truyện của tác giả ${this.authorName}. Khám phá những tác phẩm hay nhất từ ${this.authorName}.`,
      keywords: `${this.authorName}, tác giả, truyện tranh, manga, light novel`,
      url: `https://saytruyenhot.com/tac-gia/${this.authorSlug}`,
      type: 'website',
    });
  }

  private loadAuthorNovels(): void {
    this.isLoading = true;

    // Thử gọi service thật trước với parameters cần thiết
    const params = {
      page: '1',
      step: '30',
      genre: '',
      sort: '1',
      status: '0',
    };

    this.novelService.getNovels(params).subscribe({
      next: (response: IServiceResponse<any>) => {
        if (response.status === 200 && response.data) {
          // Filter novels by author (giả lập)
          const allNovels = response.data.novels || response.data.items || [];
          this.novels = allNovels
            .slice(0, 6)
            .map((novel: any) => this.convertToNovelWithAuthor(novel));
          this.calculateStatistics();
        } else {
          this.loadMockData();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.warn(
          'Failed to load novels from service, using mock data:',
          error,
        );
        this.loadMockData();
        this.isLoading = false;
      },
    });
  }

  private convertToNovelWithAuthor(novel: any): Novel {
    return {
      id: novel.id || 1,
      title: novel.title || novel.name || `Truyện của ${this.authorName}`,
      url: novel.url || novel.slug || `truyen-cua-${this.authorSlug}`,
      author: this.authorName,
      description: novel.description || `Tác phẩm hay từ ${this.authorName}`,
      coverImage:
        novel.coverImage || novel.thumbnail || '/assets/novel-placeholder.jpg',
      viewCount:
        novel.viewCount ||
        novel.view ||
        Math.floor(Math.random() * 200000 + 50000),
      status: novel.status || 1,
      rating: novel.rating || Math.random() * 2 + 3, // 3-5 stars
      updateAt: novel.updateAt || new Date().toISOString(),
      genres: novel.genres || this.generateRandomGenres(),
      chapters: novel.chapters || [],
      numChapter: 0,
      type: novel.type || false,
      wordCount: novel.wordCount || Math.floor(Math.random() * 500000 + 100000),
      translation: novel.translation || 1,
    };
  }

  private generateRandomGenres(): Genre[] {
    const availableGenres: Genre[] = [
      { id: 1, title: 'Kiếm Hiệp', slug: 'kiem-hiep', group: 1, quantity: 0 },
      { id: 2, title: 'Tiên Hiệp', slug: 'tien-hiep', group: 1, quantity: 0 },
      { id: 3, title: 'Đô Thị', slug: 'do-thi', group: 2, quantity: 0 },
      { id: 4, title: 'Ngôn Tình', slug: 'ngon-tinh', group: 3, quantity: 0 },
      { id: 5, title: 'Cung Đấu', slug: 'cung-dau', group: 3, quantity: 0 },
    ];

    const count = Math.floor(Math.random() * 3) + 1; // 1-3 genres
    return availableGenres.slice(0, count);
  }

  private loadMockData(): void {
    // Tạo dữ liệu giả lập cho tác giả
    const mockNovels: Novel[] = [
      {
        id: 1,
        title: `Kiếm Hiệp Giang Hồ - ${this.authorName}`,
        url: `kiem-hiep-giang-ho-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 125000,
        status: 1,
        rating: 4.5,
        description: `Một câu chuyện võ hiệp đầy kịch tính của tác giả ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2024-01-15').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 450000,
        translation: 1,
        genres: [
          { id: 1, title: 'Võ Hiệp', slug: 'vo-hiep', group: 1, quantity: 0 },
        ],
        chapters: [],
      },
      {
        id: 2,
        title: `Tiên Hiệp Truyền Kỳ - ${this.authorName}`,
        url: `tien-hiep-truyen-ky-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 89000,
        status: 1,
        rating: 4.2,
        description: `Hành trình tu tiên đầy thử thách từ ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2024-01-10').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 320000,
        translation: 1,
        genres: [
          {
            id: 2,
            title: 'Tiên Hiệp',
            slug: 'tien-hiep',
            group: 1,
            quantity: 0,
          },
        ],
        chapters: [],
      },
      {
        id: 3,
        title: `Đô Thị Tu La - ${this.authorName}`,
        url: `do-thi-tu-la-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 156000,
        status: 1,
        rating: 4.7,
        description: `Cuộc sống đô thị hiện đại với yếu tố siêu nhiên từ ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2024-01-20').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 600000,
        translation: 1,
        genres: [
          { id: 3, title: 'Đô Thị', slug: 'do-thi', group: 2, quantity: 0 },
        ],
        chapters: [],
      },
      {
        id: 4,
        title: `Lãng Mạn Thanh Xuân - ${this.authorName}`,
        url: `lang-man-thanh-xuan-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 67000,
        status: 1,
        rating: 4.0,
        description: `Câu chuyện tình yêu tuổi trẻ ngọt ngào từ ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2024-01-05').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 180000,
        translation: 1,
        genres: [
          { id: 4, title: 'Lãng Mạn', slug: 'lang-man', group: 3, quantity: 0 },
        ],
        chapters: [],
      },
      {
        id: 5,
        title: `Huyền Bí Cung Đình - ${this.authorName}`,
        url: `huyen-bi-cung-dinh-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 203000,
        status: 1,
        rating: 4.8,
        description: `Những âm mưu và bí mật trong hoàng cung từ ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2024-01-25').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 750000,
        translation: 1,
        genres: [
          {
            id: 5,
            title: 'Cung Đình',
            slug: 'cung-dinh',
            group: 3,
            quantity: 0,
          },
        ],
        chapters: [],
      },
      {
        id: 6,
        title: `Khoa Học Viễn Tưởng - ${this.authorName}`,
        url: `khoa-hoc-vien-tuong-${this.authorSlug}`,
        coverImage: '/assets/novel-placeholder.jpg',
        viewCount: 112000,
        status: 2,
        rating: 4.3,
        description: `Thế giới tương lai đầy công nghệ từ ${this.authorName}`,
        author: this.authorName,
        updateAt: new Date('2023-12-20').toISOString(),
        numChapter: 0,
        type: false,
        wordCount: 280000,
        translation: 1,
        genres: [
          {
            id: 6,
            title: 'Khoa Học Viễn Tưởng',
            slug: 'khoa-hoc-vien-tuong',
            group: 2,
            quantity: 0,
          },
        ],
        chapters: [],
      },
    ];

    this.novels = mockNovels;
    this.calculateStatistics();
  }

  private calculateStatistics(): void {
    this.totalNovels = this.novels.length;
    this.totalViews = this.novels.reduce(
      (sum, novel) => sum + (novel.viewCount || 0),
      0,
    );
    this.totalFollowers = Math.floor(this.totalViews * 0.15); // Estimate followers as 15% of views
    this.avgRating =
      this.novels.length > 0
        ? this.novels.reduce((sum, novel) => sum + (novel.rating || 0), 0) /
          this.novels.length
        : 0;
  }

  onNovelClick(novel: Novel): void {
    this.router.navigate(['/truyen', `${novel.url}-${novel.id}`]);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
      stars.push('full');
    }

    if (hasHalfStar) {
      stars.push('half');
    }

    while (stars.length < 5) {
      stars.push('empty');
    }

    return stars;
  }

  getStatusText(status: number): string {
    switch (status) {
      case 1:
        return 'Hoàn thành';
      case 2:
        return 'Tạm dừng';
      default:
        return 'Đang cập nhật';
    }
  }
}
