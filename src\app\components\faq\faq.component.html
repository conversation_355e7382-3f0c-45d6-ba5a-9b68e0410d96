<section class="faq-section" itemscope itemtype="https://schema.org/FAQPage">
  <div class="faq-container">
    <h2 class="faq-title">{{ title }}</h2>

    <div class="faq-list">
      <article
        *ngFor="let faq of faqs; let i = index; trackBy: trackByIndex"
        class="faq-item"
        itemscope
        itemtype="https://schema.org/Question"
        [class.expanded]="isExpanded(i)"
      >
        <button
          class="faq-question"
          [attr.aria-expanded]="isExpanded(i)"
          [attr.aria-controls]="'faq-answer-' + i"
          (click)="toggleItem(i)"
          itemprop="name"
        >
          <span class="question-text">{{ faq.question }}</span>
          <svg
            class="faq-icon"
            [class.rotated]="isExpanded(i)"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            aria-hidden="true"
          >
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        </button>

        <div
          class="faq-answer"
          [id]="'faq-answer-' + i"
          [attr.aria-hidden]="!isExpanded(i)"
          itemscope
          itemtype="https://schema.org/Answer"
        >
          <div class="answer-content" itemprop="text">
            <p [innerHTML]="faq.answer"></p>
          </div>
        </div>
      </article>
    </div>
  </div>
</section>
