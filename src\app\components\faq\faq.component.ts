import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnInit,
  ChangeDetectionStrategy,
  ViewEncapsulation,
} from '@angular/core';
import { SeoService } from '@services/seo.service';

export interface FAQItem {
  question: string;
  answer: string;
  category?: string;
  keywords?: string[];
}

@Component({
  selector: '[app-faq]',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
})
export class FaqComponent implements OnInit {
  @Input() faqs: FAQItem[] = [];
  @Input() title: string = 'Câu hỏi thường gặp';
  @Input() showStructuredData: boolean = true;

  expandedItems: Set<number> = new Set();

  constructor(private seoService: SeoService) {}

  ngOnInit() {}

  toggleItem(index: number) {
    if (this.expandedItems.has(index)) {
      this.expandedItems.delete(index);
    } else {
      this.expandedItems.add(index);
    }
  }

  isExpanded(index: number): boolean {
    return this.expandedItems.has(index);
  }

  // Track by function for performance
  trackByIndex(index: number): number {
    return index;
  }
}
