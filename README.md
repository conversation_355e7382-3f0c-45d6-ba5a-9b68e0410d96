https://mottruyen.vn/

🔹 Chức năng hiển thị và điều chỉnh
📏 Tăng/giảm kích thước chữ ✅
🎨 Đổi font chữ (Serif, Sans-serif, Mono,...) ✅
🌗 Chế độ sáng/tối (Light/Dark mode)
📄 Chế độ đọc (Dọc/Ngang)
🖼️ Ẩn/hiện hình ảnh
🔲 Chỉnh độ rộng khung đọc (Toà<PERSON> m<PERSON><PERSON> hình, giới hạn chiều rộng)
📌 Ghim nội dung quan trọng (Highlight đoạn văn, đánh dấu trang yêu thích)
🏷️ Tùy chỉnh màu nền & màu chữ ✅
🔄 Chế độ lật trang (<PERSON>ô phỏng lật sách thay vì cuộn)

🔹 Điều hướng và tương tác
⏭️ Chuyển chương tiếp theo/trước đó ✅
🔖 Lưu vị trí đọc (Bookmark) ✅
🗂️ Danh sách chương ✅
🏠 Trở về trang truyện ✅
🎯 Nhảy đến trang/chương cụ thể ✅
🕵️‍♂️ Tìm kiếm nội dung trong truyện
🔍 Xem trước nội dung chương tiếp theo
🏃 Chế độ đọc nhanh (Tăng tốc độ cuộn hoặc nhảy trang nhanh) ✅

🔹 Trải nghiệm người dùng
🔄 Cuộn tự động (Tốc độ tùy chỉnh) ✅
⏪ Lịch sử đọc ✅
🗨️ Bình luận/chia sẻ
📢 Bật/tắt thông báo chương mới
🎧 Chế độ đọc bằng giọng nói (Text-to-Speech) ✅
🎵 Bật nhạc nền khi đọc ✅
🌎 Dịch nhanh nội dung (Với truyện đa ngôn ngữ)
📚 Chế độ đọc nhiều truyện cùng lúc (Chia màn hình đọc nhiều truyện)
🔔 Lên lịch thông báo truyện yêu thích

🔹 Chức năng cá nhân hóa & giao diện
🖋️ Ghi chú trong truyện (Thêm ghi chú cá nhân vào đoạn truyện)
🎭 Chế độ đọc ẩn danh (Không lưu lịch sử đọc)
🔀 Gợi ý truyện dựa trên sở thích
🔹 Chức năng điều hướng & tương tác nâng cao
🏆 Xếp hạng chương/truyện (Đánh giá chất lượng chương)
⏳ Thống kê thời gian đọc (Thống kê số giờ đã đọc)
🗳️ Bình chọn nội dung yêu thích (Vote cho chương, nhân vật, tình tiết yêu thích)
💬 Chatroom thảo luận (Thảo luận với cộng đồng ngay khi đọc)
🔹 Chức năng hỗ trợ khác
📥 Tải xuống chương để đọc offline
📖 Chế độ đọc truyện bằng AI (Text-to-Speech nâng cao)
🔗 Kết nối tài khoản để đồng bộ trạng thái đọc (Đọc trên nhiều thiết bị)
🔑 Mở khóa chương VIP bằng điểm thưởng hoặc xem quảng cáo

🔹 Các chức năng mua bán trong novelapp (tạm thời test, chưa tích hợp bên PAYOS)

ALTER TABLE \_user
ADD COLUMN VIP BOOLEAN DEFAULT FALSE, -- Thêm cột VIP, mặc định là FALSE
ADD COLUMN VIP_Expire_at DATE, -- Thêm cột ExpireVIP kiểu DATE
ADD COLUMN Coin DECIMAL(10, 2) DEFAULT 0; -- Thêm cột Coin, mặc định là 0

CREATE TABLE transaction_history (
id SERIAL PRIMARY KEY, -- Tạo khóa chính tự động tăng
user_id INT NOT NULL, -- Khóa ngoại tham chiếu đến người dùng
type VARCHAR(50) NOT NULL, -- Loại giao dịch (deposit, withdrawal, purchase, etc.)
amount DECIMAL(10, 2) NOT NULL, -- Số tiền VNĐ hoặc coin trong giao dịch
currency VARCHAR(10) NOT NULL, -- Loại tiền tệ giao dịch(VND, COIN, etc.)
date TIMESTAMP NOT NULL, -- Thời gian giao dịch
status VARCHAR(20) NOT NULL, -- Trạng thái giao dịch (pending, completed, failed)
description TEXT, -- Mô tả giao dịch (nếu có)
payment_method VARCHAR(50), -- Phương thức giao dịch (PAYOS, Momo, VNPAY, COIN)
FOREIGN KEY (user_id) REFERENCES "user" (user_id) -- Khóa ngoại tham chiếu đến bảng "user"
);

CREATE TABLE items ( --Bán bằng tiền mặt
id SERIAL PRIMARY KEY, -- Khóa chính tự động tăng cho mỗi vật phẩm
name VARCHAR(255) NOT NULL, -- Tên vật phẩm, không được trống
description TEXT, -- Mô tả chi tiết về vật phẩm
price DECIMAL(10, 2) NOT NULL, -- Giá vật phẩm tính theo VNĐ
quantity INT DEFAULT 0, -- Số lượng vật phẩm, mặc định la 0 theo Type (Với type là VIP thì cộng theo tháng, Với Type la COIN thì cộng số lượng)
type VARCHAR(10), --VIP, COIN
status VARCHAR(20) DEFAULT 'available',-- Trạng thái vật phẩm (available, out of stock, discontinued)
image_url VARCHAR(255), -- URL hoặc tên hình ảnh của vật phẩm
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Ngày thêm vào hệ thống
CHECK (price >= 0), -- Kiểm tra giá không âm
);

--- Hiện tại có tất cả 6 items gồm:
VIP 1thang, VIP 6thang, VIP 12thang, 20xu, 50xu, 100xu

CREATE TABLE download_history (
id SERIAL PRIMARY KEY,
user_id INT NOT NULL,
novel_id INT NOT NULL,
download_date TIMESTAMP NOT NULL,
FOREIGN KEY (user_id) REFERENCES "user" (user_id),
FOREIGN KEY (novel_id) REFERENCES novels (novel_id)
)

1.---------------------------Mua bằng VND---------------------------
Các gói VIP và Mua xu sẽ thực hiện qua PayOS
PayOS có webhook để khi thanh toán thành công sẽ bắn về API của mình

Gói mua vip và xu sẽ cố định được thiết lập trên PAYOS

Yêu cầu:
Viết 1 api payment để nạp vip hoặc xu:
+Tạm thời giả định là thanh toán không thông qua payos (POST trực tiếp)
+Tham số là id trong bảng items

    +
    Tùy thuộc vào type của item mà nạp vip hay nạp xu
    nếu type là VIP thì kích hoạt vip = True và + số tháng vào ExpireVIP trong bảng User
    Nếu type là COIN thì + số lượng vào coin trong bảng User
    Đồng thời ghi lại lịch sử giao dịch vào bảng transaction_history

2.----------------------------Mua bằng xu----------------------------
Việc tải sách sẽ trừ xu của user với xu cố định là 10xu

Yêu cầu:
Thiết kế 1 api /download-novel/{novelId}
Nếu user đã download trước đó (đã mua trong download_history) thì sẽ cho phép download mà không mất xu
Nếu user chưa download trước đó (không có trong download_history) thì sẽ trừ 10xu
và thực hiện ghi vào lịch sử giao dịch transaction_history
