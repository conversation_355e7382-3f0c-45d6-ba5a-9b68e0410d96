import { Component, OnInit } from '@angular/core';

@Component({
  selector: '[app-bookcase]',
  templateUrl: './bookcase.component.html',
  styleUrls: ['./bookcase.component.scss'],
  standalone: false,
})
export class BookcaseComponent implements OnInit {
  // Mock data for demonstration
  favoriteNovels = [
    {
      id: 1,
      title: 'Tiểu thuyết mẫu 1',
      author: 'Tác giả A',
      cover: '/default_avatar.jpeg',
      lastRead: '2025-01-15',
      progress: 75,
      totalChapters: 100,
      currentChapter: 75,
    },
    {
      id: 2,
      title: 'Tiểu thuyết mẫu 2',
      author: 'Tác giả B',
      cover: '/default_avatar.jpeg',
      lastRead: '2025-01-14',
      progress: 30,
      totalChapters: 80,
      currentChapter: 24,
    },
  ];

  readingHistory = [
    {
      id: 1,
      title: '<PERSON><PERSON>ch sử đọc 1',
      author: '<PERSON><PERSON><PERSON> gi<PERSON>',
      cover: '/default_avatar.jpeg',
      lastRead: '2025-01-15 14:30',
      chapter: '<PERSON><PERSON>ơng 45: <PERSON><PERSON><PERSON><PERSON> chiến cuối cùng',
    },
    {
      id: 2,
      title: '<PERSON><PERSON><PERSON> sử đọc 2',
      author: 'T<PERSON>c giả D',
      cover: '/default_avatar.jpeg',
      lastRead: '2025-01-14 20:15',
      chapter: 'Chương 12: Khởi đầu mới',
    },
  ];

  activeTab: 'favorites' | 'history' = 'favorites';

  constructor() {}

  ngOnInit(): void {}

  switchTab(tab: 'favorites' | 'history') {
    this.activeTab = tab;
  }

  removeFromFavorites(novelId: number) {
    this.favoriteNovels = this.favoriteNovels.filter(
      (novel) => novel.id !== novelId,
    );
  }

  clearHistory() {
    this.readingHistory = [];
  }

  getProgressWidth(progress: number): string {
    return `${progress}%`;
  }
}
