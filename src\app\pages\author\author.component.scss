.novel-card {
  &:hover {
    transform: translateY(-2px);
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.author-stats {
  .stat-item {
    min-width: 80px;
  }

  @media (max-width: 640px) {
    .author-stats-numbers {
      flex-direction: column;
      gap: 1rem;
      margin-top: 1rem;
    }
  }
}

.seo-content {
  .prose {
    h2,
    h3 {
      color: inherit;
    }

    p {
      margin-bottom: 1rem;
    }

    ul {
      padding-left: 1.25rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .novel-card {
    .p-3 {
      padding: 0.75rem;
    }
  }

  .author-info {
    margin-bottom: 1rem;
  }
}
