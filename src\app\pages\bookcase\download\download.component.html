<div class="text-center h-80" *ngIf="isLoading; else loadedContent">
  <div app-spinner [sizeSpinner]="'40'"></div>
</div>
<ng-template #loadedContent>
  <div *ngIf="novels.length > 0; else empty" class="flex flex-col gap-6">
    <div class="gap-2 grid grid-cols-3 md:grid-cols-4 mt-1 lg:grid-cols-6">
      <div *ngFor="let novel of novels; let i = index">
        <div app-card-v1 [novel]="novel" (iconClick)="onDownload($event)">
          <ng-template #iconRight>
            <svg
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              class="size-4 text-white"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></g>
              <g id="SVGRepo_iconCarrier">
                <title></title>
                <g id="Complete">
                  <g id="download">
                    <g>
                      <path
                        d="M3,12.3v7a2,2,0,0,0,2,2H19a2,2,0,0,0,2-2v-7"
                        fill="none"
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                      ></path>
                      <g>
                        <polyline
                          data-name="Right"
                          fill="none"
                          id="Right-2"
                          points="7.9 12.3 12 16.3 16.1 12.3"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                        ></polyline>
                        <line
                          fill="none"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          x1="12"
                          x2="12"
                          y1="2.7"
                          y2="14.2"
                        ></line>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
          </ng-template>
        </div>
      </div>
    </div>
  </div>

  <ng-template #empty>
    <div class="w-1/3 mt-20 lg:min-h-1/2 lg:w-42 flex-center mx-auto">
      <div app-empty [message]="'Không có truyện đã tải'"></div>
    </div>
  </ng-template>
</ng-template>
