import {
  ApplicationRef,
  ComponentRef,
  Injectable,
  Type,
  ViewContainerRef,
} from '@angular/core';
// import { UserInfoPopupComponent } from '@components/common/comment/user-info-popup/user-info-popup.component';
import { ConfirmPopupComponent } from '@components/confirm-popup/confirm-popup.component';
// import { PopupDetailNovelComponent } from '@components/common/grid-novel/page/popup-detail-novel/popup-detail-novel.component';
// import { FeedbackComponent } from '@components/nav/feedback/feedback.component';
// import { ReportErrorComponent } from '@modules/chapter-page/page/report-error/report-error.component';
// import { StarRatingComponent } from '@modules/novel-detail/page/star-rating/star-rating.component';
import { IPopupComponent } from 'src/app/core/interface';

@Injectable({
  providedIn: 'root',
})
export class PopupService {
  private components: Map<number, ComponentRef<IPopupComponent>> = new Map<
    number,
    ComponentRef<IPopupComponent>
  >();
  private _viewContainerRef?: ViewContainerRef;
  constructor(private appRef: ApplicationRef) {}

  showConfirmPopup({
    title,
    message,
    confirmButtonText,
    cancelButtonText,
  }: {
    title?: string;
    message?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
  }) {
    let componentRef = this.createDynamicComponent<ConfirmPopupComponent>(
      ConfirmPopupComponent,
    );
    return componentRef?.instance.show({
      title,
      message,
      confirmButtonText,
      cancelButtonText,
    });
  }

  hashCode = function (s: string) {
    let h = 0;
    let l = s.length;
    let i = 0;
    if (l > 0) while (i < l) h = ((h << 5) - h + s.charCodeAt(i++)) | 0;
    return h;
  };

  private createDynamicComponent<T extends IPopupComponent>(
    componentType: Type<T>,
  ): ComponentRef<T> {
    let key = this.hashCode(componentType.toString());

    if (this.components.has(key)) {
      let componentRef = this.components.get(key) as ComponentRef<T>;
      if (!componentRef.hostView.destroyed) return componentRef;
      this.components.delete(key);
    }
    let componentRef =
      this._viewContainerRef?.createComponent<T>(componentType)!;
    this.components.set(key, componentRef);
    return componentRef;
  }
}
