.profile-info-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 6px 1px rgba(95, 153, 174, 0.2);
}

.dark .profile-info-container {
  background: #262626;
  color: #e5e7eb;
}

// Avatar Section
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.avatar-image {
  width: 8rem;
  height: 8rem;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #f97316;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #f97316;
  border-radius: 50%;
  padding: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.avatar-overlay:hover {
  background: #ea580c;
  transform: scale(1.05);
}

.avatar-upload-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

// User Info Section
.user-info-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.user-basic-info {
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.dark .user-basic-info {
  border-bottom-color: #374151;
}

.user-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.dark .user-name {
  color: #f9fafb;
}

.user-email {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.dark .user-email {
  color: #9ca3af;
}

.vip-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.vip-status.vip-active {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.dark .vip-status {
  background: #374151;
  color: #d1d5db;
  border-color: #4b5563;
}

// User Details Grid
.user-details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .user-details-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.dark .detail-item {
  background: #1f2937;
  border-color: #374151;
}

.detail-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .detail-label {
  color: #9ca3af;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  word-break: break-word;
}

.dark .detail-value {
  color: #f3f4f6;
}

// Action Buttons
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

// Update Form Container
.update-form-container {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.dark .update-form-container {
  border-top-color: #374151;
}

// Responsive
@media (min-width: 768px) {
  .profile-info-container {
    flex-direction: row;
    align-items: flex-start;
    gap: 3rem;
  }

  .avatar-section {
    flex-shrink: 0;
    justify-content: flex-start;
    margin-bottom: 0;
  }

  .user-info-section {
    flex: 1;
  }

  .user-basic-info {
    text-align: left;
  }

  .action-buttons {
    justify-content: flex-start;
  }
}
