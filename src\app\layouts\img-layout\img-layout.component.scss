.scroll-top-btn {
  @apply fixed right-4 bottom-4 z-[49] size-10 sm:size-12 border  rounded-xl bg-white dark:bg-neutral-700 dark:border-neutral-500 dark:text-white shadow-xl text-black flex flex-col justify-center items-center gap-1 transition-opacity duration-300;
}

.layout-container {
  @apply flex flex-col text-light-text bg-background-100 dark:bg-dark-background dark:text-dark-text;
}
.detail-bg-image-overlay {
  @apply backdrop-blur-md bg-dark-background/30 dark:bg-dark-background/40 size-full;
}
