<div
  app-img-layout
  [title]="`Đ<PERSON><PERSON> tru<PERSON>ện thể loại ${genre?.title} online`"
  [description]="`Tổng hợp truyện ${genre?.title}: ${genre?.description}.`"
>
  <!-- Structured Data for SEO -->

  <div
    app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Thể loại', url: '/the-loai' },
      { label: genre?.title, url: '/the-loai/' + genre?.slug },
    ]"
  ></div>
  <div class="flex flex-col md:flex-row gap-4">
    <div class="sm:basis-64">
      <div
        app-filter
        [isGenrePage]="true"
        [selectedValues]="selectedValues"
      ></div>
    </div>
    <div id="novels" class="relative flex-1">
      <span class="mt-2 inline-flex items-center gap-2">
        <svg
          class="size-6"
          focusable="false"
          viewBox="0 0 24 24"
          fill="currentColor"
          data-testid="LibraryBooksIcon"
        >
          <path
            d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"
          ></path>
        </svg>
        <h2 class="text-xl font-semibold uppercase">
          Truyện thuộc thể loại {{ genre?.title }}
        </h2>
      </span>

      <div
        *ngIf="isLoading; else loadedContent"
        class="text-center absolute top-1/2 left-1/2"
      >
        <div app-spinner [sizeSpinner]="'40'"></div>
      </div>
      <ng-template #loadedContent>
        <div
          class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 gap-2 mt-1"
        >
          <div
            app-card-v2
            *ngFor="let novel of novels; trackBy: trackByNovel"
            [novel]="novel"
          >
            >
          </div>
        </div>
        <div
          app-pagination
          [currentPage]="currentPage"
          [totalpage]="totalpage"
          [rootLink]="'/the-loai/' + genre?.slug"
        ></div>
      </ng-template>
    </div>
  </div>

  <!-- SEO Content Section -->
  <div class="seo-content mt-8 px-4 sm:px-2" *ngIf="genre">
    <h2 class="text-xl font-semibold mb-4">
      Truyện {{ genre.title }} Hay Nhất - Đọc Online Miễn Phí
    </h2>

    <div class="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">
      <p class="mb-4">
        <strong>Truyện {{ genre.title }}</strong> là một trong những
        <strong>thể loại truyện chữ hay nhất</strong> được độc giả yêu thích tại
        SayTruyenHot. Với
        <strong>kho truyện {{ genre.title }} lớn nhất</strong> Việt Nam, chúng
        tôi mang đến cho bạn những
        <strong>truyện {{ genre.title }} chất lượng cao</strong>,
        <strong>cập nhật nhanh nhất</strong> và hoàn toàn miễn phí.
      </p>

      <h3 class="text-lg font-semibold mt-6 mb-3">
        Đặc Điểm Nổi Bật Của Truyện {{ genre.title }}
      </h3>
      <p class="mb-4">
        {{
          genre.description ||
            "Truyện " +
              genre.title +
              " mang đến những câu chuyện hấp dẫn, lôi cuốn với nhiều tình tiết thú vị."
        }}
        <strong>Đọc truyện {{ genre.title }} online</strong> tại website của
        chúng tôi, bạn sẽ được trải nghiệm:
      </p>

      <ul class="list-disc list-inside mb-4 space-y-1">
        <li>
          <strong>Top truyện {{ genre.title }} hay nhất</strong> được tuyển chọn
          kỹ lưỡng
        </li>
        <li>
          <strong>Truyện {{ genre.title }} full hoàn thành</strong> và đang cập
          nhật
        </li>
        <li>
          <strong>Đọc truyện {{ genre.title }} không quảng cáo</strong>, giao
          diện sạch sẽ
        </li>
        <li>
          <strong>Website truyện {{ genre.title }} chất lượng cao</strong>
          với tốc độ tải nhanh
        </li>
        <li>
          <strong>Truyện {{ genre.title }} miễn phí 100%</strong> không cần đăng
          ký
        </li>
      </ul>

      <h3 class="text-lg font-semibold mt-6 mb-3">
        Tại Sao Chọn Đọc Truyện {{ genre.title }} Tại SayTruyenHot?
      </h3>
      <p class="mb-4">
        SayTruyenHot tự hào là
        <strong>website đọc truyện {{ genre.title }} số 1</strong> với:
        <strong>Kho truyện {{ genre.title }} đa dạng</strong> từ các tác giả nổi
        tiếng, <strong>cập nhật truyện {{ genre.title }} mới</strong> hàng ngày,
        và <strong>trải nghiệm đọc tuyệt vời</strong> trên mọi thiết bị.
      </p>

      <p class="text-sm text-gray-600 dark:text-gray-400 mt-6">
        Khám phá ngay <strong>truyện {{ genre.title }} hay nhất</strong> tại
        SayTruyenHot và đắm chìm trong thế giới
        <strong>{{ genre.title }} hấp dẫn</strong> với hàng nghìn tác phẩm chất
        lượng cao!
      </p>
    </div>
  </div>

  <div app-faq [faqs]="getGenreFAQs()"></div>
</div>
