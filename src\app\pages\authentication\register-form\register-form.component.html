<div app-dialog [(isVisible)]="isVisible">
  <div
    class="size-full sm:w-96 bg-white text-current rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-neutral-800 dark:border-neutral-700"
  >
    <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
      <h1
        class="text-2xl font-bold leading-tight tracking-tight md:text-3xl dark:text-white text-center"
      >
        Tạo tài k<PERSON>n
      </h1>
      <form
        class="space-y-4 md:space-y-6"
        [formGroup]="form"
        (submit)="onSubmit()"
      >
        <div>
          <label
            for="name"
            class="block mb-2 text-sm font-medium dark:text-white"
            >Tên</label
          >
          <input
            type="text"
            name="name"
            id="name"
            class="register-input"
            placeholder="Say truyen"
            required
            formControlName="name"
          />
          <div *ngIf="isControlInvalid('name')">
            <small
              *ngIf="form.get('name')?.hasError('required')"
              class="text-red-500"
              >Vui lòng nhập tên</small
            >
          </div>
        </div>
        <div>
          <label
            for="email"
            class="block mb-2 text-sm font-medium dark:text-white"
            >Email</label
          >
          <input
            type="email"
            name="email"
            id="email"
            autocomplete="email"
            class="register-input"
            placeholder="<EMAIL>"
            required
            formControlName="email"
          />
          <div *ngIf="isControlInvalid('email') && submitted">
            <small
              *ngIf="form.get('email')?.hasError('required')"
              class="text-red-500"
              >Vui lòng nhập mail đăng nhập</small
            >
            <small
              *ngIf="form.get('email')?.hasError('email') && submitted"
              class="text-red-500"
              >Vui lòng nhập đúng gmail</small
            >
          </div>
        </div>
        <div>
          <label
            for="password"
            class="block mb-2 text-sm font-medium dark:text-white"
            >Mật khẩu</label
          >

          <div class="mt-1 relative">
            <input
              formControlName="password"
              id="password"
              name="password"
              [type]="showPassword ? 'text' : 'password'"
              autocomplete="new-password"
              required
              class="register-input pr-12"
              placeholder="••••••••"
            />
            <button
              type="button"
              (click)="showPassword = !showPassword"
              class="absolute inset-y-0 right-0 flex-start px-3 text-sm font-medium text-gray-700"
              style="top: 50%; transform: translateY(-50%)"
            ></button>
          </div>
          <div *ngIf="isControlInvalid('password')">
            <small
              *ngIf="form.get('password')?.hasError('required')"
              class="text-red-500"
              >Vui lòng nhập mật khẩu</small
            >
          </div>
        </div>
        <div>
          <label
            for="confirm-password"
            class="block mb-2 text-sm font-medium dark:text-white"
            >Xác nhận mật khẩu</label
          >
          <div class="mt-1 relative">
            <input
              formControlName="confirm-password"
              name="confirm-password"
              id="confirm-password"
              [type]="showConfirmPassword ? 'text' : 'password'"
              autocomplete="new-password"
              required
              class="register-input pr-12"
              placeholder="••••••••"
            />
            <button
              type="button"
              (click)="showConfirmPassword = !showConfirmPassword"
              class="absolute inset-y-0 right-0 flex-start px-3 text-sm font-medium text-gray-700"
              style="top: 50%; transform: translateY(-50%)"
            ></button>
          </div>

          <div *ngIf="isControlInvalid('confirm-password')">
            <small
              *ngIf="form.get('confirm-password')?.hasError('required')"
              class="text-red-500"
              >Vui lòng xác nhận lại mật khẩu</small
            >
          </div>
        </div>
        <div class="flex items-start">
          <div class="flex-start h-5">
            <input
              id="terms"
              type="checkbox"
              class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 dark:bg-neutral-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
              required
              formControlName="accept"
            />
          </div>
          <div class="ml-3 text-sm">
            <label
              for="terms"
              class="font-light text-gray-500 dark:text-gray-300"
              >I accept the
              <a class="font-medium text-primary-100 hover:underline" href="#"
                >Terms and Conditions</a
              ></label
            >
          </div>
          <div *ngIf="isControlInvalid('accept')">
            <small
              *ngIf="form.get('accept')?.hasError('required')"
              class="text-red-500"
              >Vui lòng đọc và đồng ý chính sách</small
            >
          </div>
        </div>
        <button
          type="submit"
          class="w-full text-white bg-primary-100 hover:bg-primary-200 focus:ring-4 focus:ring-primary-50 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
        >
          Tạo tài khoản
        </button>
      </form>
    </div>
  </div>
</div>
