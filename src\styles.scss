// @import '@ctrl/ngx-emoji-mart/picker';
@use './styles/common.scss' as *;
@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

:root,
html,
body {
  font-family: "Be Vietnam Pro", sans-serif;
  overflow-x: hidden;
  scroll-behavior: smooth !important;
  font-weight: 300;
  --color-primary-100: #e35f66;
  --color-primary-200: #c03f4b;
  --color-primary-300: #9d1d31;
  --color-background-100: #f5f5f5;
  --color-background-200: #f5f5f5;
  --color-background-300: #f5f5f5;
}
main {
  @apply min-h-screen;
}

.content-swapper {
  @apply bg-background-100 dark:bg-dark-background text-gray-900 dark:text-gray-100;
}

.novel-header
{

  @apply z-50 top-0 right-0 left-0 flex-between h-fit transition-colors;
  
  &.header-sticky
  {
    @apply fixed bg-black/70 border-b-[1px];
  }
}
.novel-header::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0;
  width: 100%;
  height: 72px;
  z-index: 1;
  // background: linear-gradient(0deg, #000000 0%, #ffffff2b 5%, #ffffff00 80%, #3c3c3c 100%);
  @apply bg-gradient-to-b from-dark-background/60 dark:to-100% to-dark-background/[5%];
}
.cardv2-panel {
  @apply relative w-full flex bg-white  dark:bg-dark-700 rounded-xl overflow-hidden transition-colors border border-transparent hover:border-primary-100;
  @apply shadow-[0px_1px_6px_1px_rgba(0,0,0,0.1)];
}
/* Track */
::-webkit-scrollbar-track {
  // background: #f1f1f1;
  background-color: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-neutral-200 dark:bg-neutral-600 rounded-lg cursor-pointer;
}

.bg-novel-image {
  @apply absolute inset-0 size-full backdrop-blur-md;
  background: radial-gradient(ellipse at center,
      rgba(0, 0, 0, 0.3) 0%,
      /* phần giữa trong suốt */
      rgba(0, 0, 0, 0.3) 50%,
      /* phần giữa trong suốt */
      rgba(0, 0, 0, 0.8) 100%
      /* 4 góc mờ đen */
    );

    &::after {
      content: "";
      position: absolute;
      bottom: 0px;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      // background: linear-gradient(0deg, #000000 0%, #ffffff2b 5%, #ffffff00 80%, #3c3c3c 100%);
      @apply bg-gradient-to-t from-[var(--color-background-100)] dark:from-dark-background to-[10px] to-transparent;
    }
}



.genre-tag {
  @apply border-primary-100 border text-primary-300 text-xs font-normal bg-neutral-300/20 dark:bg-dark-650 dark:text-dark-50 rounded-md shadow-sm px-2 py-0.5 uppercase;
}

.common-container {
  @apply md:container mx-auto p-2 mt-20;
}

.brand-name {
  font-weight: 700;
  background: linear-gradient(135deg, #ea580c, #c65f5f);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}


/* add the code bellow */
@layer utilities {

  .scrollbar-style-lg::-webkit-scrollbar {
      width: 8px;
      height: 8px;
  }
  .scrollbar-style-md::-webkit-scrollbar {
      width: 6px;
      height: 6px;
  }
  .scrollbar-style-sm::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
  /* === Size === */


  /* === FLEX HELPERS === */

  .inline-flex-center {
    @apply inline-flex items-center justify-center;
  }

  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  .flex-col-center {
    @apply flex flex-col items-center justify-center;
  }

  .flex-col-between {
    @apply flex flex-col justify-between;
  }

  /* === GRID HELPERS === */
  .grid-auto-fit {
    @apply grid grid-cols-[repeat(auto-fit, minmax(250px, 1fr))];
  }

  .grid-auto-fill {
    @apply grid grid-cols-[repeat(auto-fill, minmax(200px, 1fr))];
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* === Border HELPERS === */
  .border-common {
    @apply border border-neutral-200 dark:border-neutral-700 rounded-lg;
  }
}