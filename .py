import re
import os

# Self-closing <app-xxx ... />
pattern_self = re.compile(r"<app-([a-z0-9-]+)([^>]*)/>", re.IGNORECASE)

# Cặp thẻ <app-xxx ...>...</app-xxx> (cho phép xuống dòng trước dấu >)
pattern_pair = re.compile(
    r"<app-([a-z0-9-]+)([^>]*)>([\s\S]*?)</app-\1\s*>", re.IGNORECASE
)


def process_file(path: str):
    with open(path, "r", encoding="utf-8") as f:
        content = f.read()

    new_content = content

    # Replace self-closing trước
    new_content = pattern_self.sub(r"<div app-\1\2></div>", new_content)

    # Replace cặp mở/đóng
    new_content = pattern_pair.sub(r"<div app-\1\2>\3</div>", new_content)

    if new_content != content:
        with open(path, "w", encoding="utf-8") as f:
            f.write(new_content)
        print(f"Updated: {path}")


def process_directory(root_dir: str):
    for root, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith(".html"):
                process_file(os.path.join(root, file))


if __name__ == "__main__":
    project_dir = "./src/app"  # 👉 chỉnh path cho đúng
    process_directory(project_dir)
