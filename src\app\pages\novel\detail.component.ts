import { isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ComponentRef,
  Inject,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalComponent } from '@components/modals/modal/modal.component';
import { ReportErrorComponent } from '@components/report-error/report-error.component';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { FeatureFlags } from '@schemas/enum';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { IServiceResponse } from '@schemas/ResponseType';
import { AccountService } from '@services/account.service';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { FeatureFlagsService } from '@services/feature-flags.service';
import { NovelService } from '@services/novel.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import globalConfig from 'globalConfig';
import { Subject, takeUntil, forkJoin, of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

@Component({
  selector: 'main[app-detail]',
  standalone: false,
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements OnInit, OnDestroy {
  novel?: Novel;
  similarNovels: Novel[] = [];
  sameAuthorNovels: Novel[] = [];
  lastChapter: Chapter | null = null;
  descriptionExpanded: boolean = false;
  isLoadingSimilarNovel: boolean = false;
  isLoadingCoAuthor: boolean = false;
  modalDownloadRef?: ComponentRef<ModalComponent>;
  breadcrumbLinks: { label: string | undefined; url: string | undefined }[] =
    [];
  destroy$ = new Subject<void>();
  currentPage = 1;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private novelService: NovelService,
    private accountService: AccountService,
    private dynamicService: DynamicLoadingService,
    private seoService: SeoService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object,
    private toastService: ToastService,
    private featureFlags: FeatureFlagsService,
  ) {}
  ngOnInit(): void {
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if (!this.novel) return;

      const page = params.get('page')?.replace('trang-', '') || 1;
      if (!Number.isInteger(Number(page))) {
        this.router.navigate(['/not-found']);
        return;
      }
      this.currentPage = Number(page);
      const title =
        this.currentPage > 1
          ? `Truyện ${this.novel.title} - Trang ${this.currentPage}`
          : `Truyện ${this.novel.title}`;
      this.seoService.setTitle(title);
    });

    this.route.data.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      const novelRes = params['novelRes'] as IServiceResponse<Novel>;
      const chaptersRes = params[
        'chaptersRes'
      ] as IServiceResponse<ChapterList>;

      this.novel = novelRes?.data;
      if (!novelRes || novelRes.status === 0 || !this.novel) {
        this.router.navigate(['/not-found']);
        return;
      }

      this.lastChapter = chaptersRes?.data?.chapters?.[0] || null;
      this.currentPage =
        Number(
          this.route.snapshot.paramMap.get('page')?.replace('trang-', ''),
        ) || 1;
      // Critical path - setup SEO immediately
      this.setUpSEO(this.novel);
      this.setupBreadcrumbs(this.novel);

      // Non-critical - defer secondary data loading
      if (!this.isServer()) {
        this.loadSecondaryData();
      } else {
        // For SSR, load author novels synchronously
        this.loadCoAuthorNovels();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.modalDownloadRef?.destroy();
  }

  private loadSecondaryData(): void {
    // Load both secondary data sources in parallel
    const coAuthor$ = this.novel?.author
      ? this.novelService
          .getNovelCoAuthor(this.novel.id)
          .pipe(
            catchError(() =>
              of({ data: undefined, status: 0 } as IServiceResponse<NovelList>),
            ),
          )
      : of({ data: undefined, status: 0 } as IServiceResponse<NovelList>);

    const similar$ = this.novelService
      .getSimilarNovel(this.novel!.id)
      .pipe(
        catchError(() =>
          of({ data: undefined, status: 0 } as IServiceResponse<Novel[]>),
        ),
      );

    this.isLoadingSimilarNovel = true;
    this.isLoadingCoAuthor = true;

    forkJoin({
      coAuthor: coAuthor$,
      similar: similar$,
    })
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoadingSimilarNovel = false;
          this.isLoadingCoAuthor = false;
        }),
      )
      .subscribe({
        next: ({ coAuthor, similar }) => {
          if (coAuthor.data) {
            this.sameAuthorNovels = coAuthor.data.novels;
          }
          if (similar.data) {
            this.similarNovels = similar.data;
          }
          this.cd.markForCheck();
        },
        error: (error) => {
          console.error('Error loading secondary data:', error);
        },
      });
  }

  private loadCoAuthorNovels(): void {
    if (!this.novel?.author) return;

    this.isLoadingCoAuthor = true;
    this.novelService
      .getNovelCoAuthor(this.novel.id)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => (this.isLoadingCoAuthor = false)),
      )
      .subscribe((res: IServiceResponse<NovelList>) => {
        if (res.data) {
          this.sameAuthorNovels = res.data.novels;
          this.cd.markForCheck();
        }
      });
  }

  onFollow() {
    if (!this.accountService.isAuthenticated()) {
      this.toastService.show(
        ToastType.Error,
        `Bạn hãy <a class="text-blue-400 hover:underline">đăng nhập</a> để theo dõi truyện nhé!`,
        5000,
      );
      return;
    }

    this.accountService
      .Follow(this.novel?.id!, !this.novel?.isFollow)
      .pipe(takeUntil(this.destroy$))
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.novel!.isFollow = !this.novel?.isFollow;
          this.cd.markForCheck();

          const message = this.novel?.isFollow
            ? `Đã theo dõi <strong>${this.novel?.title}</strong>`
            : `Đã bỏ theo dõi <strong>${this.novel?.title}</strong>`;

          this.toastService.show(ToastType.Success, message);
        }
      });
  }
  onReport() {
    this.dynamicService
      .createDynamicComponent<ReportErrorComponent>(ReportErrorComponent)
      ?.instance?.open(this.novel!);
  }

  onDownload() {
    if (!this.featureFlags.isEnabled(FeatureFlags.FEATURE_DOWNLOAD_NOVEL)) {
      this.toastService.show(
        ToastType.Success,
        `Tính năng tải truyện đang phát triển`,
      );
      return;
    }

    if (!this.accountService.isAuthenticated() || !this.novel) {
      this.toastService.show(
        ToastType.Error,
        `Bạn cần <a class="text-blue-400 hover:underline">đăng nhập</a> để tải truyện nhé!`,
        5000,
      );
      return;
    }
    let flag = false;
    if (!this.modalDownloadRef || !this.modalDownloadRef.instance) flag = true;

    this.modalDownloadRef =
      this.dynamicService.createDynamicComponent<ModalComponent>(
        ModalComponent,
      );
    if (this.modalDownloadRef && this.modalDownloadRef.instance) {
      this.modalDownloadRef.instance.title = `Tải PDF`;
      this.modalDownloadRef.instance.content = `<div class="mt-2"> <p class="text-sm">Bạn cần tốn <b>10 xu</b> để tải <span class="text-primary-100">[${this.novel?.title}]</span>.PDF<br />(Lưu vĩnh viễn trên tài khoản, có thể tải lại khi cần).</p></div>`;
    }

    this.modalDownloadRef?.instance?.openModal();
    this.modalDownloadRef?.instance?.confirm?.subscribe(() => {
      this.confirmDownload();
    });
  }

  confirmDownload() {
    if (this.accountService.user!.coin < 10) {
      this.toastService.show(
        ToastType.Error,
        `Xu không đủ, vui lòng nạp thêm xu để tải truyện`,
        5000,
      );
      return;
    }

    const toastid = this.toastService.show(
      ToastType.Success,
      `Đang tải <strong>${this.novel?.title}</strong>. Vui lòng chờ trong giây lát!`,
      10000,
    );

    this.accountService
      .downloadNovel(this.novel?.id!)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: Blob) => {
          this.toastService.stop(toastid);
          const blob = new Blob([data], { type: data.type });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${this.novel?.url}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // Clean up
          window.URL.revokeObjectURL(url);
        },
        error: (err) => {
          this.toastService.clear();
          this.toastService.show(
            ToastType.Error,
            `Đã xảy ra lỗi, vui lòng thử lại`,
            5000,
          );
        },
      });
  }
  toggleDescription() {
    this.descriptionExpanded = !this.descriptionExpanded;
  }

  onReadButtonClick(novel: Novel) {
    if (!this.lastChapter) {
      return;
    }
    this.router.navigate([
      '/',
      novel.url,
      'chuong-' + this.lastChapter.slug!,
      this.lastChapter.id,
    ]);
  }
  setUpSEO(novel: Novel) {
    const genreNames = novel.genres.map((v) => v.title).join(', ');

    // Enhanced title with competitive keywords
    const title =
      this.currentPage > 1
        ? `${novel.title} - Trang ${this.currentPage} | Đọc Truyện Online Miễn Phí Hay Nhất 2025`
        : `Đọc Truyện ${novel.title} Online Miễn Phí | ${novel.author || 'Tác Giả'} - Top ${genreNames} 2025`;

    // Enhanced description with long-tail keywords
    const statusText = novel.status === 1 ? 'hoàn thành' : 'đang cập nhật';
    const chapterText = `${novel.numChapter} chương`;
    const description = `Đọc truyện ${novel.title} của tác giả ${novel.author || 'ẩn danh'} thuộc thể loại ${genreNames} online miễn phí tại website số 1 Việt Nam. ${novel.description ? novel.description.substring(0, 120) + '...' : `Truyện ${genreNames} hay nhất, chất lượng cao, cập nhật nhanh nhất.`} ${chapterText}, ${statusText}. Đọc không quảng cáo, hỗ trợ offline, giao diện đẹp, trải nghiệm tuyệt vời!`;

    // Enhanced keywords with competitive terms
    const competitiveKeywords = [
      `đọc truyện ${novel.title} online miễn phí`,
      `truyện ${novel.title} full hoàn thành`,
      `${novel.title} ${novel.author || 'tác giả'}`,
      `top truyện ${genreNames} hay nhất`,
      `${novel.title} cập nhật nhanh nhất`,
      `truyện ${genreNames} chất lượng cao`,
      `đọc ${novel.title} không quảng cáo`,
      `${novel.title} hỗ trợ offline`,
      `website truyện số 1 Việt Nam`,
      `kho truyện ${genreNames} lớn nhất`,
      novel.status === 1
        ? `${novel.title} truyện full`
        : `${novel.title} truyện hot`,
      `${novel.title} đọc mượt mà`,
    ];

    const url = `${globalConfig.BASE_URL}/truyen/${novel.url}-${novel.id}`;
    const imageUrl = novel.coverImage || `${globalConfig.BASE_URL}/logo.png`;

    const seoData = {
      title,
      description,
      type: 'book' as const,
      image: imageUrl,
      url,
      siteName: globalConfig.APP_NAME,
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const,
      // keywords removed - using content-based SEO instead
      author: novel.author || 'Tác giả ẩn danh',
      publishedTime: novel.updateAt,
      modifiedTime: novel.updateAt,
      tags: novel.genres.map((v) => v.title),
      canonical: url,
      section: 'Truyện chữ',
      category: novel.genres.map((v) => v.title).join(', '),
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.seoService.generateNovelSchema(novel),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Truyện', url: '/truyen' },
        { name: novel.title, url: `/truyen/${novel.url}-${novel.id}` },
      ]),
    ];

    this.seoService.addStructuredData(schemas);
  }

  setupBreadcrumbs(novel: Novel) {
    this.breadcrumbLinks = [
      { label: 'Trang chủ', url: '/' },
      { label: 'Truyện', url: '/truyen' },
      { label: novel.title, url: undefined }, // Current page, no link
    ];
  }

  isServer() {
    return isPlatformServer(this.platformId);
  }

  onImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    target.src =
      'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg';
  }

  // TrackBy functions for better performance
  trackGenreBy(index: number, genre: any): number {
    return genre.id;
  }

  trackNovelBy(index: number, novel: Novel): number {
    return novel.id;
  }
}
