<div class="relative flex h-full w-full">
  <div class="relative flex size-full overflow-x-clip">
    <ng-container>
      <div
        *ngFor="let item of items; let i = index"
        class="absolute size-full"
        [style.transform]="attrs[i].transform"
        [ngClass]="{
          hidden: attrs[i].hidden,
          'transition-transform duration-500 ease-in-out': attrs[i].animation,
        }"
      >
        <ng-template
          [ngTemplateOutlet]="itemTemplate || null"
          [ngTemplateOutletContext]="{ $implicit: item }"
        ></ng-template>
      </div>
    </ng-container>
  </div>
  <ng-container>
    <ng-template
      [ngTemplateOutlet]="navTemplate || null"
      [ngTemplateOutletContext]="{ $implicit: index }"
    ></ng-template>
  </ng-container>
</div>
<ng-content />
