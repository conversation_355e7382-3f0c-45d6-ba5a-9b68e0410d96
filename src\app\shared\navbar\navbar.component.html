
  <div
    class="z-50 xl:container relative mx-auto size-full flex-between my-2 px-2 xl:px-0"
  >
    <a
      [routerLink]="['/']"
      class="flex-start"
      title="SayTruyenHot - Trang chủ"
      aria-label="Về trang chủ"
    >
      <div
        class="group hover:bg-white bg-gray-800 text-white rounded-full size-12 flex-center"
      >
        <img
          class="size-full"
          src="/logo.png"
          alt="SayTruyenHot - Nền tảng đọc truyện chữ hàng đầu Việt Nam"
          width="48"
          height="48"
        />
      </div>
      <div class="hidden xs:block brand-name text-xl ml-2">SayTruyenHot</div>
    </a>

    <div
      class="flex ml-5 text-white gap-6 w-full h-12 justify-start text-md items-center"
    >
      <button
        #genresBtn
        class="xl:flex hidden space-x-2 cursor-pointer h-full items-center"
        aria-label="Thể loại"
        (click)="genresVisible = !genresVisible"
      >
        <svg
          fill="currentColor"
          class="size-5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <path
              d="M10 3H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm10 10h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zM17 3c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4zM7 13c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4z"
            ></path>
          </g>
        </svg>
        <p class="font-normal">Thể loại</p>
      </button>

      <a
        class="xl:flex hidden space-x-2 cursor-pointer"
        routerLink="/tim-kiem"
        title="Tìm kiếm truyện nâng cao"
        aria-label="Tìm kiếm truyện"
      >
        <svg
          class="size-5"
          viewBox="-1.2 -1.2 26.40 26.40"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g id="SVGRepo_tracerCarrier"></g>
          <g id="SVGRepo_iconCarrier">
            <path
              d="M3 2a1 1 0 1 0-2 0v20a1 1 0 1 0 2 0V2ZM5 6.6A2.6 2.6 0 0 1 7.6 4h12.8A2.6 2.6 0 0 1 23 6.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 8.4V6.6ZM5 15.6A2.6 2.6 0 0 1 7.6 13h4.8a2.6 2.6 0 0 1 2.6 2.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 17.4v-1.8Z"
            ></path>
          </g>
        </svg>
        <p class="font-normal">Danh sách</p>
      </a>
      <a
        class="xl:flex hidden space-x-2 cursor-pointer"
        routerLink="/tu-sach/theo-doi"
        title="Tủ sách theo dõi của bạn"
        aria-label="Xem tủ sách theo dõi"
      >
        <svg
          class="size-5 hover:fill-current hover:text-white"
          viewBox="0 0 256 256"
          fill="currentColor"
          id="Flat"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M208.00244,24h-136a32.03667,32.03667,0,0,0-32,32V224a8.00039,8.00039,0,0,0,8,8h144a8,8,0,0,0,0-16h-136a16.01833,16.01833,0,0,1,16-16h136a8.00039,8.00039,0,0,0,8-8V32A8.00039,8.00039,0,0,0,208.00244,24Zm-24,96.001L158.394,100.7998a4.0005,4.0005,0,0,0-4.7998,0L128.00244,119.999V40h56Z"
            ></path>
          </g>
          <g id="SVGRepo_iconCarrier">
            <path
              d="M208.00244,24h-136a32.03667,32.03667,0,0,0-32,32V224a8.00039,8.00039,0,0,0,8,8h144a8,8,0,0,0,0-16h-136a16.01833,16.01833,0,0,1,16-16h136a8.00039,8.00039,0,0,0,8-8V32A8.00039,8.00039,0,0,0,208.00244,24Zm-24,96.001L158.394,100.7998a4.0005,4.0005,0,0,0-4.7998,0L128.00244,119.999V40h56Z"
            ></path>
          </g>
        </svg>
        <p class="font-normal">Tủ sách</p>
      </a>
      <a
        class="flex space-x-2 cursor-pointer"
        aria-label="vip"
        routerLink="/price"
      >
        <svg
          class="size-10"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M27 7H5a2 2 0 00-2 2v14a2 2 0 002 2h22a2 2 0 002-2V9a2 2 0 00-2-2z"
            ></path>
            <path
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 13l3 6.5 3-6.5M16.5 13v6.5M20.5 17.275h1.48c.651 0 1.277-.275 1.721-.75a2.338 2.338 0 00.215-2.932 1.394 1.394 0 00-1.14-.593H20.5v4.275zm0 0V19.5"
            ></path>
          </g>
        </svg>
      </a>
    </div>

    <div class="flex-start gap-4">
      <div app-search-bar></div>
      <div
        #themebtn
        (click)="toggleTheme()"
        class="bg-white/40 text-white rounded-full size-8 flex-center cursor-pointer dark:bg-dark-700"
      >
        <svg
          class="size-5 darkmode-icon"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <circle cx="12" cy="12" r="4" />
          <path
            d="M3 12h1M12 3v1M20 12h1M12 20v1M5.6 5.6l.7 .7M18.4 5.6l-.7 .7M17.7 17.7l.7 .7M6.3 17.7l-.7 .7"
          />
        </svg>

        <svg
          class="size-4 lightmode-icon bi bi-moon-stars-fill"
          viewBox="-0.96 -0.96 17.92 17.92"
          xmlns="http://www.w3.org/2000/svg"
          fill="#ffffff"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <title id="nightModeIconTitle">Night Mode</title>
            <path
              d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"
            ></path>
            <path
              d="M10.794 3.148a.217.217 0 0 1 .412 0l.387 1.162c.173.518.579.924 1.097 1.097l1.162.387a.217.217 0 0 1 0 .412l-1.162.387a1.734 1.734 0 0 0-1.097 1.097l-.387 1.162a.217.217 0 0 1-.412 0l-.387-1.162A1.734 1.734 0 0 0 9.31 6.593l-1.162-.387a.217.217 0 0 1 0-.412l1.162-.387a1.734 1.734 0 0 0 1.097-1.097l.387-1.162zM13.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.156 1.156 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.156 1.156 0 0 0-.732-.732l-.774-.258a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732L13.863.1z"
            ></path>
          </g>
        </svg>
      </div>
      <div
        (appClickOutside)="showAccPopup = false"
        class="bg-neutral-800 rounded-full size-10 flex-center"
      >
        <button
          appTutorial
          class="rounded-full"
          (click)="toggleAccount()"
          aria-label="Tài khoản"
        >
          @if (curUser) {
            <img
              src="{{ curUser.avatar }}"
              (error)="
                curUser!.avatar =
                  'https://cdn.anhtruyen.com/?data=****************************************************************************************************************************'
              "
              class="w-10 h-10 rounded-full object-cover"
              alt="avatar"
            />
          } @else {
            <svg
              class="animate-pulse h-6 w-6 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          }
        </button>
        <div *ngIf="showAccPopup" [@fadeInOut] class="user-popup">
          <span
            class="text-black flex flex-row justify-center items-center dark:text-white font-medium select-none w-full"
          >
            <span
              class="size-16 flex-shrink-0 bg-slate-200 rounded-full flex-start"
            >
              <img
                [src]="curUser?.avatar"
                class="object-cover size-16 rounded-full"
                (error)="
                  curUser!.avatar =
                    'https://cdn.anhtruyen.com/?data=****************************************************************************************************************************'
                "
                alt="avatar"
              />
            </span>
            @if (!curUser) {
              <span class="flex-col flex-center w-full p-2">
                <p class="text-primary-100">Bạn chưa đăng nhập</p>
                <p class="text-sm font-light">
                  Hãy đăng nhập để làm nhiều trò mèo nhé :v
                </p>
              </span>
            } @else {
              <span class="flex-col flex w-full p-2">
                <p class="text-primary-100">Xin chào {{ curUser.firstName }}</p>
                <p class="text-sm font-light">
                  Đặc quyền VIP: <span class="font-medium">chưa có</span>
                </p>
                <p class="text-sm font-light">
                  Số xu: <span class="font-medium"> {{ curUser.coin }}</span>
                </p>
              </span>
            }
          </span>
          <span class="border-b border-gray-200 w-full h-2 flex"></span>

          <div class="grid grid-cols-2 gap-2 font-medium">
            <button
              appTutorial
              [order]="1"
              (click)="openSetting()"
              class="hover:bg-neutral-100 dark:hover:bg-neutral-700 p-1 rounded-lg flex-center gap-1"
              aria-label="Cài đặt"
            >
              <span>
                <svg
                  class="size-6"
                  viewBox="0 0 1024 1024"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      fill="currentColor"
                      d="M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088-24.512 11.968a294.113 294.113 0 0 0-34.816 20.096l-22.656 15.36-116.224-25.088-65.28 113.152 79.68 88.192-1.92 27.136a293.12 293.12 0 0 0 0 40.192l1.92 27.136-79.808 88.192 65.344 113.152 116.224-25.024 22.656 15.296a294.113 294.113 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152 24.448-11.904a288.282 288.282 0 0 0 34.752-20.096l22.592-15.296 116.288 25.024 65.28-113.152-79.744-88.192 1.92-27.136a293.12 293.12 0 0 0 0-40.256l-1.92-27.136 79.808-88.128-65.344-113.152-116.288 24.96-22.592-15.232a287.616 287.616 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384zm0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z"
                    ></path>
                  </g>
                </svg>
              </span>
              Cài đặt
            </button>
            <button
              class="btn-login hover:bg-neutral-100 dark:hover:bg-neutral-700 py-2 rounded-lg flex-center gap-1"
              (click)="goToBookcase()"
              aria-label="Tủ sách"
            >
              <span>
                <svg
                  class="size-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      d="M4 19V6.2C4 5.0799 4 4.51984 4.21799 4.09202C4.40973 3.71569 4.71569 3.40973 5.09202 3.21799C5.51984 3 6.0799 3 7.2 3H16.8C17.9201 3 18.4802 3 18.908 3.21799C19.2843 3.40973 19.5903 3.71569 19.782 4.09202C20 4.51984 20 5.0799 20 6.2V17H6C4.89543 17 4 17.8954 4 19ZM4 19C4 20.1046 4.89543 21 6 21H20M9 7H15M9 11H15M19 17V21"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </g>
                </svg>
              </span>
              Tủ sách
            </button>
          </div>
          <button
            (click)="goToVip()"
            class="btn-login hover:bg-neutral-100 dark:hover:bg-neutral-700 flex flex-row gap-1 justify-between px-4 py-2 rounded font-medium"
            aria-label="Đăng ký thành viên VIP"
          >
            Đăng ký thành viên VIP
            <span
              class="text-white bg-amber-500 px-2 py-1 rounded font-semibold text-xs"
              ><svg
                class="size-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
              >
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g
                  id="SVGRepo_tracerCarrier"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                ></g>
                <g id="SVGRepo_iconCarrier">
                  <g>
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path
                      d="M2.8 5.2L7 8l4.186-5.86a1 1 0 0 1 1.628 0L17 8l4.2-2.8a1 1 0 0 1 1.547.95l-1.643 13.967a1 1 0 0 1-.993.883H3.889a1 1 0 0 1-.993-.883L1.253 6.149A1 1 0 0 1 2.8 5.2zM12 15a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"
                    ></path>
                  </g>
                </g></svg
            ></span>
          </button>
          <button
            (click)="goToCoin()"
            class="hover:bg-neutral-100 dark:hover:bg-neutral-700 flex flex-row gap-1 justify-between px-4 py-2 rounded font-medium"
            aria-label="Nạp xu"
          >
            Nạp xu
            <span
              class="bg-neutral-200 dark:bg-neutral-800 px-2 py-1 rounded font-semibold text-xs"
            >
              <svg
                class="size-5"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" />
                <circle cx="12" cy="12" r="9" />
                <path
                  d="M14.8 9a2 2 0 0 0 -1.8 -1h-2a2 2 0 0 0 0 4h2a2 2 0 0 1 0 4h-2a2 2 0 0 1 -1.8 -1"
                />
                <path d="M12 6v2m0 8v2" />
              </svg>
            </span>
          </button>
          <div class="flex flex-col gap-2">
            @if (curUser) {
              <button
                class="btn btn-primary"
                (click)="goToProfile()"
                aria-label="Trang cá nhân"
              >
                Trang cá nhân
              </button>
            } @else {
              <button
                class="btn btn-primary"
                (click)="login()"
                aria-label="Đăng nhập"
              >
                Đăng nhập
              </button>
              <button
                class="btn-register hover:bg-neutral-200 dark:hover:bg-neutral-700 py-2 rounded-lg font-semibold"
                (click)="register()"
                aria-label="Đăng ký"
              >
                Đăng ký
              </button>
            }
            @if (curUser) {
              <button
                class="btn-register hover:bg-neutral-200 dark:hover:bg-neutral-700 py-2 rounded-lg font-semibold"
                (click)="logout()"
                aria-label="Đăng xuất"
              >
                Đăng xuất
              </button>
            }
          </div>
        </div>
      </div>
      <!-- From Uiverse.io by vinodjangid07 -->

      <div
        (appClickOutside)="showMenuPopup = false"
        class="flex-center size-10 bg-dark-800 rounded-xl xl:hidden hover:cursor-pointer group"
      >
        <span (change)="toggleMenue()">
          <input
            type="checkbox"
            id="menu-popup-checkbox"
            [checked]="showMenuPopup"
          />
          <label for="menu-popup-checkbox" class="menu-popup-toggle">
            <div class="bars" id="bar1"></div>
            <div class="bars" id="bar2"></div>
            <div class="bars" id="bar3"></div>
          </label>
        </span>
        <div *ngIf="showMenuPopup" [@fadeInOut] class="list-popup">
          <button
            id="genresBtn2"
            (click)="genresVisible = true; showMenuPopup = false"
            class="p-1 flex gap-2"
            aria-label="Thể loại"
          >
            <svg
              fill="currentColor"
              class="size-5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></g>
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M10 3H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm10 10h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zM17 3c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4zM7 13c-2.206 0-4 1.794-4 4s1.794 4 4 4 4-1.794 4-4-1.794-4-4-4z"
                ></path>
              </g>
            </svg>
            Thể loại
          </button>
          <a
            class="p-1 flex gap-2"
            routerLink="/tim-kiem"
            title="Tìm kiếm truyện nâng cao"
            aria-label="Tìm kiếm truyện"
          >
            <svg
              class="size-5"
              viewBox="-1.2 -1.2 26.40 26.40"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g id="SVGRepo_tracerCarrier"></g>
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M3 2a1 1 0 1 0-2 0v20a1 1 0 1 0 2 0V2ZM5 6.6A2.6 2.6 0 0 1 7.6 4h12.8A2.6 2.6 0 0 1 23 6.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 8.4V6.6ZM5 15.6A2.6 2.6 0 0 1 7.6 13h4.8a2.6 2.6 0 0 1 2.6 2.6v1.8a2.6 2.6 0 0 1-2.6 2.6H7.6A2.6 2.6 0 0 1 5 17.4v-1.8Z"
                ></path>
              </g>
            </svg>
            Danh sách
          </a>
          <a
            class="p-1 flex gap-2"
            routerLink="/tu-sach/theo-doi"
            title="Tủ sách theo dõi của bạn"
            aria-label="Xem tủ sách theo dõi"
          >
            <svg
              class="size-5 hover:fill-current hover:text-white"
              viewBox="0 0 256 256"
              fill="currentColor"
              id="Flat"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g
                id="SVGRepo_tracerCarrier"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M208.00244,24h-136a32.03667,32.03667,0,0,0-32,32V224a8.00039,8.00039,0,0,0,8,8h144a8,8,0,0,0,0-16h-136a16.01833,16.01833,0,0,1,16-16h136a8.00039,8.00039,0,0,0,8-8V32A8.00039,8.00039,0,0,0,208.00244,24Zm-24,96.001L158.394,100.7998a4.0005,4.0005,0,0,0-4.7998,0L128.00244,119.999V40h56Z"
                ></path>
              </g>
              <g id="SVGRepo_iconCarrier">
                <path
                  d="M208.00244,24h-136a32.03667,32.03667,0,0,0-32,32V224a8.00039,8.00039,0,0,0,8,8h144a8,8,0,0,0,0-16h-136a16.01833,16.01833,0,0,1,16-16h136a8.00039,8.00039,0,0,0,8-8V32A8.00039,8.00039,0,0,0,208.00244,24Zm-24,96.001L158.394,100.7998a4.0005,4.0005,0,0,0-4.7998,0L128.00244,119.999V40h56Z"
                ></path>
              </g>
            </svg>
            Tủ sách
          </a>
        </div>
      </div>
    </div>
    @defer (when genresVisible) {
      <div
        app-genres
        #genres
        (appClickOutside)="genresVisible = false"
        [elementIgnore]="genresBtn"
        [elementIdIgnore]="'genresBtn2'"
        [disabled]="!genresVisible"
        class="absolute top-[56px] left-0 w-full lg:w-[42rem] xl:w-[44rem]"
        [(visible)]="genresVisible"
        [routerLinkGenres]="true"
        (genreSelected)="genresVisible = false"
      ></div>
    }
  </div>
