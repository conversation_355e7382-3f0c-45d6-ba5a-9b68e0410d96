export class ServiceUtility {
  // constructor() { }
  static fillDescription(
    description: string | null | undefined,
    params?: { url: string; id: number; title: string },
    link = true,
  ): string {
    if (!description && params) {
      let url = `/truyen/${params.url}-${params.id}`;
      let content = !link
        ? params.title
        : `<a class="text-blue-400 hover:underline" href = "${url}"> ${params.title} </a>`;
      return `Đọc truyện ${content} bản full đầy đủ chap mới nhất tại - SayTruyenHot.
      Bạn đọc đừng quên để lại bình luận và chia sẻ, <PERSON>ng hộ Mê truyện mới ra các chương mới nhất của truyện ${content}.`;
    }
    return description!;
  }
  static fillSeoDescription(
    description: string | null | undefined,
    params?: { title: string },
  ): string {
    if (!description && params) {
      let content = params.title;
      return `Đọ<PERSON> truyện ${content} bản full đầy đủ chap mới nhất với hình <PERSON>nh sắc nét, truyện tải nhanh, không quảng cáo tại website đọc truyện tranh online - SayTruyenHot.`;
    }
    if (description!.length > 160) {
      return ServiceUtility.getFirstSentence(description!);
    }
    return description!;
  }

  static getFirstSentence(text: string): string {
    // Tìm dấu chấm đầu tiên
    let endOfFirstSentence = text.indexOf('.');

    // Nếu có dấu chấm, cắt từ đầu đến dấu chấm đó
    if (endOfFirstSentence !== -1) {
      return text.substring(0, endOfFirstSentence + 1); // Cộng 1 để lấy dấu chấm
    }

    // Nếu không có dấu chấm, trả về nguyên văn
    return text;
  }
}
