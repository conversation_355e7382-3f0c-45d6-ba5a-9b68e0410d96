<div
  class="overflow-hidden bg-white dark:bg-dark-bg rounded-lg absolute right-0 bottom-0 h-[350px] w-[250px] z-20 block shadow-lg"
>
  <div class="flex p-3 gap-2 overflow-auto">
    <div
      *ngFor="let emoji of emoji_contents.slice(0, 5)"
      class="cursor-pointer transition-opacity duration-200"
      [class.opacity-100]="activate === emoji.id"
      [class.opacity-50]="activate !== emoji.id"
      [class.border-b-4]="activate === emoji.id"
      [class.border-primary-100]="activate === emoji.id"
      (click)="selectPacket(emoji.id)"
    >
      <img class="max-h-[39px]" [src]="emoji.img" alt="Emoji" />
    </div>

    <button
      (click)="togglePopover()"
      class="p-2 rounded hover:bg-gray-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
    >
      <svg
        class="h-4 w-4 text-gray-500"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path stroke="none" d="M0 0h24v24H0z" />
        <polyline points="6 9 12 15 18 9" />
      </svg>
    </button>
  </div>

  <div
    [ngClass]="{ hidden: !isPopoverOpen }"
    class="absolute top-10 left-0 bg-white dark:bg-neutral-800 shadow-lg rounded-lg p-3 w-64"
  >
    <div class="flex flex-col">
      <div
        *ngFor="let emoji of emoji_contents.slice(5)"
        class="p-2 cursor-pointer hover:bg-gray-200 dark:hover:bg-neutral-700 rounded-lg flex-start gap-2"
        (click)="selectPacket(emoji.id)"
      >
        <img class="w-6 h-6" [src]="emoji.img" alt="Emoji" />
        <span class="text-xs font-bold text-center">{{ emoji.describe }}</span>
      </div>
    </div>
  </div>

  <div class="gap-1 overflow-y-auto grid grid-cols-4 h-full p-2">
    <ng-container *ngIf="getEmojiList().length > 0">
      <div
        *ngFor="let emoji of getEmojiList(); let i = index"
        class="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg"
        (click)="handleEmojiClick(i + 1)"
      >
        <img
          class="size-[60px]"
          [src]="getEmojiPath(activate, i + 1)"
          alt="Emoji"
          loading="lazy"
        />
      </div>
    </ng-container>
  </div>
</div>
