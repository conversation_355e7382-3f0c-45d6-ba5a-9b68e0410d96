import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { SelectComponent } from '@components/select/select.component';
import { Novel } from '@schemas/Novel';

@Component({
  selector: '[app-grid-card]',
  imports: [CommonModule, NovelCardV1Component, SelectComponent],
  templateUrl: './grid-card.component.html',
  styleUrl: './grid-card.component.scss',
})
export class GridCardComponent {
  @Output() selectedValueChange = new EventEmitter<any>();
  @Input() options: any[] = [];
  @Input() novels: Novel[] = [];
  @Input() title = 'TRUYỆN FULL';
  constructor() {}
}
