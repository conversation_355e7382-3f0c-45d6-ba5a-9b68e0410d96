<div
  app-img-layout
  [title]="'Tủ Sách'"
  [description]="'Quản lý và theo dõi những truyện yêu thích của bạn'"
>
  <div
    app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Tủ Sách', url: '/tu-sach' },
      { label: tabs[selectedTab].name, url: tabs[selectedTab].routerLink },
    ]"
  ></div>
  <div
    app-tabs
    [tabs]="tabs"
    [selectedTab]="selectedTab"
    (selectedTabChange)="onTabChange($event)"
    class="flex mb-2"
  ></div>
  <router-outlet></router-outlet>
</div>
