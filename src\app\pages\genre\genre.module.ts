import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { GenrePageComponent } from './genre-page.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { FaqComponent } from '@components/faq/faq.component';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { EmptyComponent } from '@components/empty/empty.component';
import { SelectComponent } from '@components/select/select.component';
import { GenresComponent } from '@components/genres/genres.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { FilterComponent } from '@components/filter/filter.component';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { ImgLayoutComponent } from "@layouts/img-layout/img-layout.component";

const routes: Routes = [
  { path: '', component: GenrePageComponent },
  {
    path: ':page',
    component: GenrePageComponent,
  },
];

@NgModule({
  declarations: [GenrePageComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    BreadcrumbComponent,
    NovelCardV1Component,
    FaqComponent,
    NovelCardV2Component,
    SpinnerComponent,
    EmptyComponent,
    SelectComponent,
    GenresComponent,
    ClickOutsideDirective,
    FilterComponent,
    FaqComponent,
    PaginationComponent,
    ImgLayoutComponent
],
})
export class GenreModule {}
