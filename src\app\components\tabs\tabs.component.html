<div class="w-full relative z-[1]">
  <button
    *ngFor="let tab of tabs; let i = index"
    (click)="selectTab(tab.id)"
    title="{{ tab.name }}"
    [attr.aria-label]="tab.name"
    class="inline-flex gap-2 py-2 px-2 mr-2 font-semibold transition-colors border-primary-300"
    [ngClass]="{
      'border-b-[2px] text-primary-100': selectedTab === tab.id,
    }"
  >
    <span [innerHTML]="tab.icon | safeHtml"></span>
    {{ tab.name }}
  </button>

  <span
    class="-z-10 absolute bottom-0 left-0 right-0 border h-0 bg-neutral-500 dark:border-neutral-800"
  ></span>
</div>
