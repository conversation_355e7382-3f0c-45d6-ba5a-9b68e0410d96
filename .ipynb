{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from paddleocr import PaddleOCR\n", "\n", "ocr = PaddleOCR(use_angle_cls=True, lang=\"vi\")  # hoặc lang='vi' nếu có tiếng Việt\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detected: input_path\n", "Detected: page_index\n", "Detected: doc_preprocessor_res\n", "Detected: dt_polys\n", "Detected: model_settings\n", "Detected: text_det_params\n", "Detected: text_type\n", "Detected: text_rec_score_thresh\n", "Detected: rec_texts\n", "Detected: rec_scores\n", "Detected: rec_polys\n", "Detected: vis_fonts\n", "Detected: textline_orientation_angles\n", "Detected: rec_boxes\n"]}], "source": ["results = ocr.predict(\"ngay-dem-an-cuu-vi.jpg\")\n", "\n", "for line in results[0]:\n", "    text = line[1][0]\n", "    print(f\"Detected: {line}\")\n", "    if \"truyenfull.vision\" in text.lower():\n", "        print(\"✅ Found 'truyenfull.vision'\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pytrends\n", "  Downloading pytrends-4.9.2-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: requests>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pytrends) (2.32.4)\n", "Requirement already satisfied: pandas>=0.25 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pytrends) (2.3.1)\n", "Requirement already satisfied: lxml in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pytrends) (6.0.0)\n", "Requirement already satisfied: numpy>=1.26.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pandas>=0.25->pytrends) (2.2.5)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pandas>=0.25->pytrends) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pandas>=0.25->pytrends) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from pandas>=0.25->pytrends) (2025.2)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.0->pytrends) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.0->pytrends) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.0->pytrends) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python313\\lib\\site-packages (from requests>=2.0->pytrends) (2025.7.14)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from python-dateutil>=2.8.2->pandas>=0.25->pytrends) (1.17.0)\n", "Downloading pytrends-4.9.2-py3-none-any.whl (15 kB)\n", "Installing collected packages: pytrends\n", "Successfully installed pytrends-4.9.2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 24.3.1 -> 25.2\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["!pip install pytrends"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON>ng thể lấy dữ liệu, status code: 400\n"]}], "source": ["import httpx\n", "import pandas as pd\n", "\n", "def fetch_trend_json():\n", "    params = {\n", "        # Bạn phải điều chỉnh URL và params dựa vào link thật copy từ DevTools\n", "        'hl': 'vi-VN',\n", "        'tz': -420,  # tư<PERSON>ng ứng GMT+7\n", "        'req': '{\"comparisonItem\":[{\"keyword\":\"truyện chữ\",\"geo\":\"VN\",\"time\":\"today 12-m\"}],\"category\":0,\"property\":\"\"}',\n", "        'tz': '420'\n", "    }\n", "    url = \"https://trends.google.com/trends/api/widgetdata/multiline\"\n", "    resp = httpx.get(url, params=params)\n", "    text = resp.text.lstrip(\")]}',\\n\")  # loại bỏ prefix bảo mật nếu có\n", "    return resp, text\n", "\n", "response, json_str = fetch_trend_json()\n", "\n", "if response.status_code == 200:\n", "    data = response.json()\n", "    # Tr<PERSON><PERSON> xuất tập dữ liệu túm gọn về thời gian và giá trị interest\n", "    timeline = data['default']['timelineData']\n", "    df = pd.DataFrame([\n", "        {\n", "            'time': pd.to_datetime(item['time'], unit='s'),\n", "            'value': item['value'][0]\n", "        }\n", "        for item in timeline\n", "    ])\n", "    print(df.head())\n", "else:\n", "    print(\"<PERSON><PERSON><PERSON>ng thể lấy dữ liệu, status code:\", response.status_code)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Response [429 unknown]>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["await httpx.AsyncClient().get(\"https://trends.google.com.vn/trends/explore?geo=VN&q=truy%E1%BB%87n%20ch%E1%BB%AF&hl=vi\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}