import { CommonModule } from '@angular/common';
import {
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { CommentComponent } from './comment.component';
import { EmojiComponent } from './emoji/emoji.component';
import { UserInfoPopupComponent } from './user-info-popup/user-info-popup.component';
import { DateAgoPipe } from 'src/app/shared/pines/date-ago.pine';

@NgModule({
  declarations: [CommentComponent, UserInfoPopupComponent, EmojiComponent],
  exports: [CommentComponent, UserInfoPopupComponent, EmojiComponent],
  imports: [CommonModule, DateAgoPipe, RouterLink, ReactiveFormsModule],
  providers: [provideHttpClient(withInterceptorsFromDi())],
})
export class CommentModule {}
