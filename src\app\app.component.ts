import { isPlatformServer, ViewportScroller } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Inject,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation,
} from '@angular/core';
import { NavigationEnd, Router, Scroll } from '@angular/router';
import { SettingType } from '@schemas/SettingType.enum';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { EventService } from '@services/event.service';
import { SeoService } from '@services/seo.service';
import { SettingService } from '@services/setting.service';
import { filter } from 'rxjs';

@Component({
  selector: '[app-root]',
  templateUrl: './app.component.html',
  standalone: false,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'content-swapper',
  },
})
export class AppComponent implements OnInit {
  title = 'novels-app';
  scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
  themValue = '';

  @ViewChild('dynamicContainer', { read: ViewContainerRef })
  dynamicContainer?: ViewContainerRef;

  constructor(
    private router: Router,
    private viewportScroller: ViewportScroller,
    private seoService: SeoService,
    public settingService: SettingService,
    public eventService: EventService,
    private dynamicLoadingService: DynamicLoadingService,
    private renderer: Renderer2,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {
    if (isPlatformServer(this.platformId)) return;

    this.setupScrollBehavior();
    // this.trackPageViews();
  }
  ngOnInit() {
    this.setupBaseSeo();
    if (isPlatformServer(this.platformId)) return;

    this.settingService
      .OnSettingChange(SettingType.Theme)
      .subscribe((themeSetting) => {
        console.log(themeSetting.value);

        if (themeSetting.value == 'dark') {
          this.renderer.addClass(document.body, 'dark');
        } else {
          this.renderer.removeClass(document.body, 'dark');
        }
      });
    this.settingService
      .OnSettingChange(SettingType.BgColor)
      .subscribe((bgSetting) => {
        this.changeBgColor(
          bgSetting.options?.find((o) => o.value == bgSetting.value).colors,
        );
      });
    this.settingService
      .OnSettingChange(SettingType.PrimaryColor)
      .subscribe((primarySetting) => {
        this.changePrimaryColor(
          primarySetting.options?.find((o) => o.value == primarySetting.value)
            .colors,
        );
      });
  }

  ngAfterViewInit() {
    if (isPlatformServer(this.platformId)) return;
    this.dynamicLoadingService.viewContainerRef = this.dynamicContainer;
  }

  changePrimaryColor(colors: { '100': string; '200': string; '300': string }) {
    document.body.style.setProperty('--color-primary-100', colors['100']);
    document.body.style.setProperty('--color-primary-200', colors['200']);
    document.body.style.setProperty('--color-primary-300', colors['300']);
  }
  changeBgColor(colors: { '100': string; '200': string; '300': string }) {
    document.body.style.setProperty('--color-background-100', colors['100']);
    document.body.style.setProperty('--color-background-200', colors['200']);
    document.body.style.setProperty('--color-background-300', colors['300']);
  }

  private setupBaseSeo() {
    this.seoService.addCommonSchema();
  }

  private setupScrollBehavior() {
    let canScroll = true;
    this.router.events
      .pipe(filter((e) => e instanceof Scroll || e instanceof NavigationEnd))
      .subscribe((e: any) => {
        if (e instanceof NavigationEnd) {
          const nav = this.router.getCurrentNavigation();
          canScroll = nav?.extras.state?.['canScroll'] ?? true;
          return;
        }
        if (!canScroll) {
          canScroll = true;
          return;
        }
        if (e.position) {
          // backward navigation
          window.scrollTo({
            top: e.position[1],
            left: e.position[0],
            behavior: 'instant',
          });
        } else if (e.anchor) {
          this.viewportScroller.scrollToAnchor(e.anchor);
        } else {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        }
      });
  }

  private trackPageViews() {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Track page views for analytics if needed
        if (typeof (window as any).gtag !== 'undefined') {
          (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
            page_path: event.urlAfterRedirects,
          });
        }
      });
  }
}
