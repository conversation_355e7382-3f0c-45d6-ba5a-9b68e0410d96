import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TabsComponent } from '@components/tabs/tabs.component';
import { BookcaseComponent } from './bookcase.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { ImgLayoutComponent } from '@layouts/img-layout/img-layout.component';

@NgModule({
  declarations: [BookcaseComponent],
  imports: [
    CommonModule,
    TabsComponent,
    BreadcrumbComponent,
    ImgLayoutComponent,
    RouterModule.forChild([
      {
        path: '',
        component: BookcaseComponent,
        children: [
          {
            path: '',
            redirectTo: 'theo-doi',
            pathMatch: 'full',
          },
          {
            path: 'theo-doi',
            loadComponent: () =>
              import('./follow/follow.component').then(
                (m) => m.FollowComponent,
              ),
          },
          {
            path: 'da-mua',
            loadComponent: () =>
              import('./download/download.component').then(
                (m) => m.DownloadComponent,
              ),
          },
        ],
      },
    ]),
  ],
})
export class BookcaseModule {}
