.darkmode-icon {
  @apply hidden;
}

.lightmode-icon {
  @apply block;
}

.dark-mode {
  .darkmode-icon {
    @apply block;
  }
  .lightmode-icon {
    @apply hidden;
  }
}

.user-popup {
  @apply origin-top transition-opacity absolute flex flex-col gap-2 top-[56px] right-0 bg-slate-50 dark:bg-dark-700 shadow-2xl p-4 space-y-1 w-80 rounded-lg;
  &::before {
    @apply content-[''] absolute -top-[10px] right-16 xl:right-0 transform translate-x-[-50%] border-b-[10px] border-white dark:border-neutral-700;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
  }
}
.list-popup {
  @apply origin-top transition-opacity absolute flex flex-col gap-2 top-[56px] rounded-lg right-0 bg-slate-50 dark:bg-dark-700 shadow-2xl p-3 space-y-1 z-[100] rounded-b;
  &::before {
    @apply content-[''] absolute -top-[10px] right-2 transform translate-x-[-50%] border-b-[10px] border-white dark:border-neutral-700;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
  }
}



#menu-popup-checkbox {
  @apply hidden;
}

.menu-popup-toggle {
  @apply relative size-6 flex flex-col items-center justify-center gap-1.5 cursor-pointer transition duration-500;
}

.bars {
  @apply w-full h-0.5 bg-slate-100 rounded-lg;
}

#bar1,
#bar3 {
  @apply w-[80%];
}

#bar2 {
  @apply transition duration-200;
}

#menu-popup-checkbox:checked + .menu-popup-toggle {
  @apply rotate-180 transition duration-200;
}

#menu-popup-checkbox:checked + .menu-popup-toggle .bars {
  @apply absolute transition duration-200;
}

#menu-popup-checkbox:checked + .menu-popup-toggle #bar2 {
  @apply scale-x-0 transition duration-200;
}

#menu-popup-checkbox:checked + .menu-popup-toggle #bar1 {
  @apply w-full rotate-45 transition duration-200;
}

#menu-popup-checkbox:checked + .menu-popup-toggle #bar3 {
  @apply w-full -rotate-45 transition duration-200;
}
