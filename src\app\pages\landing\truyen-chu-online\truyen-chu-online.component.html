<div class="landing-page">
  <!-- Breadcrumb -->
  <!-- <div app-breadcrumb [Links]="breadcrumbLinks" class="mb-6"></div> -->

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">
        <PERSON><PERSON><PERSON> Truyện Chữ Online Miễn <PERSON>
        <span class="hero-highlight">Kho Truyện Hay Nhất 2025</span>
      </h1>
      <p class="hero-description">
        Khám phá hơn <strong>50,000+ bộ truyện chữ</strong> hay nhất với đầy đủ
        thể loại: ngô<PERSON> tình, ti<PERSON><PERSON> hiệp, ki<PERSON><PERSON> hiệ<PERSON>, đ<PERSON> thị, huy<PERSON><PERSON> huy<PERSON>.
        <strong>Cập nhật 24/7</strong>, không quảng cáo, đọc mượt mà trên mọi
        thiết bị.
      </p>
      <div class="hero-stats">
        <div class="stat-item">
          <span class="stat-number">50,000+</span>
          <span class="stat-label">Bộ truyện</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">24/7</span>
          <span class="stat-label">Cập nhật</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">100%</span>
          <span class="stat-label">Miễn phí</span>
        </div>
      </div>
      <div class="hero-actions">
        <a routerLink="/" class="btn-primary"> Khám phá ngay </a>
        <a routerLink="/tim-kiem" class="btn-secondary"> Tìm kiếm truyện </a>
      </div>
    </div>
  </section>

  <!-- Popular Genres Section -->
  <section class="genres-section">
    <div class="section-header">
      <h2 class="section-title">Thể Loại Truyện Phổ Biến</h2>
      <p class="section-description">
        Khám phá các thể loại truyện chữ được yêu thích nhất tại SayTruyenHot
      </p>
    </div>

    <div class="genres-grid">
      <article
        *ngFor="let genre of popularGenres; trackBy: trackByGenre"
        class="genre-card"
        itemscope
        itemtype="https://schema.org/Thing"
      >
        <a
          [routerLink]="['/the-loai', genre.slug]"
          class="genre-link"
          [title]="'Đọc truyện ' + genre.name + ' hay nhất'"
        >
          <h3 class="genre-name" itemprop="name">{{ genre.name }}</h3>
          <p class="genre-description" itemprop="description">
            {{ genre.description }}
          </p>
          <span class="genre-arrow">→</span>
        </a>
      </article>
    </div>
  </section>

  <!-- Featured Novels Section -->
  <section class="featured-section" *ngIf="featuredNovels.length > 0">
    <div class="section-header">
      <h2 class="section-title">Truyện Đề Xuất Hàng Đầu</h2>
      <p class="section-description">
        Những bộ truyện chữ được đánh giá cao và yêu thích nhất
      </p>
    </div>

    <div class="novels-grid">
      <div
        app-card-v1
        *ngFor="let novel of featuredNovels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></div>
    </div>

    <div class="section-footer">
      <a routerLink="/" class="view-more-link"> Xem thêm truyện hay → </a>
    </div>
  </section>

  <!-- Benefits Section -->
  <section class="benefits-section">
    <div class="section-header">
      <h2 class="section-title">Tại Sao Chọn SayTruyenHot?</h2>
    </div>

    <div class="benefits-grid">
      <div class="benefit-item">
        <div class="benefit-icon">📚</div>
        <h3 class="benefit-title">Kho Truyện Khổng Lồ</h3>
        <p class="benefit-description">
          Hơn 50,000+ bộ truyện chữ đa dạng thể loại, từ cổ điển đến hiện đại
        </p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">🚀</div>
        <h3 class="benefit-title">Cập Nhật Nhanh Nhất</h3>
        <p class="benefit-description">
          Cập nhật chương mới 24/7, không bao giờ bỏ lỡ nội dung hot nhất
        </p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">💯</div>
        <h3 class="benefit-title">Hoàn Toàn Miễn Phí</h3>
        <p class="benefit-description">
          Đọc không giới hạn, không quảng cáo làm phiền, trải nghiệm tuyệt vời
        </p>
      </div>

      <div class="benefit-item">
        <div class="benefit-icon">📱</div>
        <h3 class="benefit-title">Đa Nền Tảng</h3>
        <p class="benefit-description">
          Đọc mượt mà trên máy tính, điện thoại, tablet với giao diện thân thiện
        </p>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <div
    app-faq
    [faqs]="faqs"
    [title]="'Câu Hỏi Thường Gặp Về Đọc Truyện Chữ Online'"
    [showStructuredData]="true"
    class="faq-wrapper"
  ></div>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2 class="cta-title">Bắt Đầu Hành Trình Đọc Truyện Ngay Hôm Nay!</h2>
      <p class="cta-description">
        Tham gia cộng đồng hàng triệu độc giả tại SayTruyenHot
      </p>
      <div class="cta-actions">
        <a routerLink="/" class="btn-primary"> Khám phá truyện hay </a>
        <a routerLink="/auth/register" class="btn-secondary">
          Đăng ký miễn phí
        </a>
      </div>
    </div>
  </section>
</div>
